import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormGroup, FormControl } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Router } from '@angular/router';
import { of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';
import { DatePipe } from '@angular/common';
// eslint-disable-next-line @typescript-eslint/naming-convention
import CreateMawbFromHawbComponent from './create-mawb-from-hawb.component';
import { UserProfileService } from '@shared/services/user-profile.service';
import { SliCreateRequestService } from '../../../sli-mgmt/services/sli-create-request.service';
import { MawbCreateRequestService } from '../../services/mawb-create-request.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { NotificationService } from '@shared/services/notification.service';
import { DropDownType } from '@shared/models/dropdown-type.model';
import { EmitCharges } from '../../models/other-charges.model';

describe('CreateMawbFromHawbComponent', () => {
	let component: CreateMawbFromHawbComponent;
	let fixture: ComponentFixture<CreateMawbFromHawbComponent>;

	// Service spies
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;
	let sliCreateRequestServiceSpy: jasmine.SpyObj<SliCreateRequestService>;
	let mawbCreateRequestServiceSpy: jasmine.SpyObj<MawbCreateRequestService>;
	let orgMgmtRequestServiceSpy: jasmine.SpyObj<OrgMgmtRequestService>;
	let notificationServiceSpy: jasmine.SpyObj<NotificationService>;
	let routerSpy: jasmine.SpyObj<Router>;
	let datePipeSpy: jasmine.SpyObj<DatePipe>;

	// Component method spies
	let getCurrentUserSpy: jasmine.Spy;

	// Test data constants
	const MOCK_SLI_DETAIL = {
		shipmentParty: [],
		departureLocation: 'LAX',
		arrivalLocation: 'JFK',
		textualHandlingInstructions: 'Handle with care',
		pieces: [],
		totalGrossWeight: 100,
		rateCharge: { currencyUnit: 'USD', numericalValue: 50 },
		waybillPrefix: 'TEST',
		waybillNumber: '123456',
		accountingInformation: 'Test accounting',
		rateClassCode: 'N',
		totalVolumetricWeight: 80,
		goodsDescription: 'Test goods',
		carrierDeclarationDate: new Date().toISOString(),
		carrierDeclarationPlace: 'Test place',
		consignorDeclarationSignature: 'Test signature',
		carrierDeclarationSignature: 'Carrier signature',
	};

	const MOCK_HAWB_DETAIL = {
		shipmentParty: [],
		sliPartyList: [],
		partyList: [],
		departureLocation: 'LAX',
		arrivalLocation: 'JFK',
		waybillPrefix: 'AWB',
		waybillNumber: '12345',
		sliId: 'test-sli-id',
		orgId: 'test-org-id',
		rateCharge: { currencyUnit: 'USD', numericalValue: 50 },
		totalGrossWeight: 100,
		rateClassCode: 'N',
		totalVolumetricWeight: 80,
		goodsDescription: 'Test goods',
		carrierDeclarationDate: new Date().toISOString(),
		carrierDeclarationPlace: 'Test place',
		consignorDeclarationSignature: 'Test signature',
		carrierDeclarationSignature: 'Carrier signature',
		textualHandlingInstructions: 'Handle with care',
		otherChargeList: [],
	};

	const MOCK_ORG_INFO = {
		id: 'test-org',
		companyName: 'Test Organization',
		partyRole: 'AIRLINE',
		countryCode: 'US',
		locationName: '123 Test St',
		regionCode: 'CA',
		textualPostCode: '90210',
		cityCode: 'LA',
		persons: [{ phoneNumber: '************', emailAddress: '<EMAIL>' }],
		iataCargoAgentCode: 'TEST123',
	};

	const MOCK_USER = {
		primaryOrgId: 'test-org-id',
		userId: 'user123',
		email: '<EMAIL>',
		firstName: 'Test',
		lastName: 'User',
	};

	/**
	 * Helper function to create service spies with default return values
	 */
	function createServiceSpies(): void {
		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		sliCreateRequestServiceSpy = jasmine.createSpyObj('SliCreateRequestService', [
			'getCurrencies',
			'getSliDetail',
			'getAirports',
			'getCountries',
		]);
		mawbCreateRequestServiceSpy = jasmine.createSpyObj('MawbCreateRequestService', [
			'getHawbDetail',
			'getHawbDetailBatch',
			'getMawbDetail',
			'createMawb',
			'updateMawb',
		]);
		orgMgmtRequestServiceSpy = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgInfo', 'getOrgList']);
		notificationServiceSpy = jasmine.createSpyObj('NotificationService', ['showSuccess', 'showError']);
		routerSpy = jasmine.createSpyObj('Router', ['navigate', 'getCurrentNavigation']);
		datePipeSpy = jasmine.createSpyObj('DatePipe', ['transform']);
	}

	/**
	 * Helper function to setup default spy return values
	 */
	function setupDefaultSpyReturns(): void {
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));
		sliCreateRequestServiceSpy.getCurrencies.and.returnValue(of(['USD', 'EUR', 'GBP']));
		sliCreateRequestServiceSpy.getAirports.and.returnValue(
			of([
				{ code: 'LAX', name: 'Los Angeles' },
				{ code: 'JFK', name: 'New York' },
				{ code: 'LHR', name: 'London' },
			] as any)
		);
		sliCreateRequestServiceSpy.getCountries.and.returnValue(
			of([
				{ code: 'US', name: 'United States' },
				{ code: 'UK', name: 'United Kingdom' },
				{ code: 'CA', name: 'Canada' },
			] as any)
		);
		sliCreateRequestServiceSpy.getSliDetail.and.returnValue(of(MOCK_SLI_DETAIL as any));
		mawbCreateRequestServiceSpy.getHawbDetail.and.returnValue(of(MOCK_HAWB_DETAIL as any));
		mawbCreateRequestServiceSpy.getHawbDetailBatch.and.returnValue(of([MOCK_HAWB_DETAIL as any]));
		mawbCreateRequestServiceSpy.getMawbDetail.and.returnValue(of(MOCK_HAWB_DETAIL as any));
		mawbCreateRequestServiceSpy.createMawb.and.returnValue(of('success'));
		mawbCreateRequestServiceSpy.updateMawb.and.returnValue(of('success'));
		orgMgmtRequestServiceSpy.getOrgInfo.and.returnValue(of(MOCK_ORG_INFO as any));
		orgMgmtRequestServiceSpy.getOrgList.and.returnValue(
			of([
				{ id: 'carrier1', prefix: 'AA', name: 'American Airlines' },
				{ id: 'carrier2', prefix: 'DL', name: 'Delta Airlines' },
			] as any)
		);
		datePipeSpy.transform.and.returnValue('2023-01-01 10:00:00');
		routerSpy.getCurrentNavigation.and.returnValue(null);
	}

	/**
	 * Helper function to create mock form groups for child components
	 */
	function createMockChildComponentForms(): any {
		const mockCarrierAgentForm = new FormGroup({
			carrierCode: new FormControl(''),
			carrierName: new FormControl(''),
			company: new FormControl(''),
			agentIataCode: new FormControl(''),
			country: new FormControl(''),
		});

		const mockAirportInfoForm = new FormGroup({
			origin: new FormControl(''),
			destination: new FormControl(''),
			departureAndRequestedRouting: new FormControl(''),
			airportOfDestination: new FormControl(''),
			amountOfInsurance: new FormControl(''),
			wtOrVal: new FormControl(''),
			other: new FormControl(''),
			declaredValueForCarriage: new FormGroup({
				currencyUnit: new FormControl('USD'),
				numericalValue: new FormControl('200'),
			}),
			declaredValueForCustoms: new FormGroup({
				currencyUnit: new FormControl('USD'),
				numericalValue: new FormControl('100'),
			}),
		});

		const mockOtherChargesForm = new FormGroup({
			charges: new FormControl(''),
		});

		const mockPrepaidForm = new FormGroup({
			prepaid: new FormControl(''),
			weightChargePrepaid: new FormControl(''),
			weightChargeCollect: new FormControl(''),
			valuationChargePrepaid: new FormControl(''),
			valuationChargeCollect: new FormControl(''),
			taxPrepaid: new FormControl(''),
			taxCollect: new FormControl(''),
		});

		const mockCollectForm = new FormGroup({
			collect: new FormControl(''),
		});

		return {
			mockCarrierAgentForm,
			mockAirportInfoForm,
			mockOtherChargesForm,
			mockPrepaidForm,
			mockCollectForm,
		};
	}

	beforeEach(async () => {
		createServiceSpies();
		setupDefaultSpyReturns();

		await TestBed.configureTestingModule({
			imports: [
				CreateMawbFromHawbComponent,
				ReactiveFormsModule,
				MatDialogModule,
				MatFormFieldModule,
				MatInputModule,
				MatSelectModule,
				MatButtonModule,
				MatDatepickerModule,
				MatNativeDateModule,
				BrowserAnimationsModule,
				TranslateModule.forRoot(),
			],
			providers: [
				provideHttpClient(),
				provideHttpClientTesting(),
				{ provide: UserProfileService, useValue: userProfileServiceSpy },
				{ provide: SliCreateRequestService, useValue: sliCreateRequestServiceSpy },
				{ provide: MawbCreateRequestService, useValue: mawbCreateRequestServiceSpy },
				{ provide: OrgMgmtRequestService, useValue: orgMgmtRequestServiceSpy },
				{ provide: NotificationService, useValue: notificationServiceSpy },
				{ provide: Router, useValue: routerSpy },
				{ provide: DatePipe, useValue: datePipeSpy },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(CreateMawbFromHawbComponent);
		component = fixture.componentInstance;

		// Initialize carriers to prevent errors in fillMawbInfo
		component.carriers = [
			{ id: 'carrier-1', prefix: 'AA', name: 'American Airlines' },
			{ id: 'carrier-2', prefix: 'DL', name: 'Delta Airlines' },
		] as any;

		// Set up component method spies
		getCurrentUserSpy = spyOn(component, 'getCurrentUser').and.returnValue(of(null));

		// Setup mock child component forms
		setupMockChildComponents();
	});

	afterEach(() => {
		// Ensure carriers is always defined to prevent errors during cleanup
		if (!component.carriers) {
			component.carriers = [];
		}
	});

	/**
	 * Helper function to setup mock child components with form groups
	 */
	function setupMockChildComponents(): void {
		const forms = createMockChildComponentForms();

		// Add spies to form methods
		spyOn(forms.mockCarrierAgentForm, 'patchValue').and.callThrough();
		spyOn(forms.mockAirportInfoForm, 'patchValue').and.callThrough();
		spyOn(forms.mockOtherChargesForm, 'patchValue').and.callThrough();
		spyOn(forms.mockPrepaidForm, 'patchValue').and.callThrough();
		spyOn(forms.mockCollectForm, 'patchValue').and.callThrough();

		// Assign mock components to component instance
		component.carrierAgentComponent = {
			carrierAgentForm: forms.mockCarrierAgentForm,
			getFormData: jasmine.createSpy('getFormData').and.returnValue({}),
		} as any;

		component.airPortInfoComponent = {
			airportInfoForm: forms.mockAirportInfoForm,
		} as any;

		component.otherChargesComponent = {
			otherChargesForm: forms.mockOtherChargesForm,
			otherChargesList: [],
			initOtherCharges: jasmine.createSpy('initOtherCharges'),
		} as any;

		component.prepaidCollectComponent = {
			prepaidForm: forms.mockPrepaidForm,
			collectForm: forms.mockCollectForm,
		} as any;

		component.issuedByComponent = {
			getData: jasmine.createSpy('getData').and.returnValue({}),
		} as any;

		// Mock shipperOrConsigneeInfoComponentList
		const mockShipperComponent = {
			shipperConsigneeForm: forms.mockCarrierAgentForm,
			getFormData: jasmine.createSpy('getFormData').and.returnValue({}),
		};
		const mockConsigneeComponent = {
			shipperConsigneeForm: forms.mockCarrierAgentForm,
			getFormData: jasmine.createSpy('getFormData').and.returnValue({}),
		};

		component.shipperOrConsigneeInfoComponentList = {
			first: mockShipperComponent,
			last: mockConsigneeComponent,
			forEach: jasmine.createSpy('forEach').and.callFake((callback: any) => {
				callback(mockShipperComponent);
				callback(mockConsigneeComponent);
			}),
		} as any;

		// Initialize component properties
		component.hawbList = [{ hawbId: 'test-hawb-id-1' }, { hawbId: 'test-hawb-id-2' }] as any;
		component.alsoNotifies = [];
		component.carriers = [
			{ id: 'carrier1', prefix: 'AA', name: 'American Airlines', orgType: 'CARRIER' },
			{ id: 'carrier2', prefix: 'DL', name: 'Delta Airlines', orgType: 'CARRIER' },
		] as any;
	}

	afterEach(() => {
		// Reset service spies to prevent Observable subscription errors
		if (sliCreateRequestServiceSpy?.getSliDetail) {
			sliCreateRequestServiceSpy.getSliDetail.and.returnValue(of(MOCK_SLI_DETAIL as any));
		}
		if (mawbCreateRequestServiceSpy?.getHawbDetail) {
			mawbCreateRequestServiceSpy.getHawbDetail.and.returnValue(of(MOCK_HAWB_DETAIL as any));
		}
		if (mawbCreateRequestServiceSpy?.getHawbDetailBatch) {
			mawbCreateRequestServiceSpy.getHawbDetailBatch.and.returnValue(of([MOCK_HAWB_DETAIL as any]));
		}
		fixture?.destroy();
	});

	afterAll(() => {
		// Final cleanup to prevent subscription errors
		if (sliCreateRequestServiceSpy) {
			sliCreateRequestServiceSpy.getSliDetail?.and.returnValue(of(MOCK_SLI_DETAIL as any));
			sliCreateRequestServiceSpy.getCurrencies?.and.returnValue(of(['USD', 'EUR', 'GBP']));
		}
		if (mawbCreateRequestServiceSpy?.getHawbDetail) {
			mawbCreateRequestServiceSpy.getHawbDetail.and.returnValue(of(MOCK_HAWB_DETAIL as any));
		}
		if (mawbCreateRequestServiceSpy?.getHawbDetailBatch) {
			mawbCreateRequestServiceSpy.getHawbDetailBatch.and.returnValue(of([MOCK_HAWB_DETAIL as any]));
		}
		if (orgMgmtRequestServiceSpy?.getOrgInfo) {
			orgMgmtRequestServiceSpy.getOrgInfo.and.returnValue(of(MOCK_ORG_INFO as any));
		}
	});

	describe('Component Initialization', () => {
		it('should create component successfully', () => {
			expect(component).toBeTruthy();
			expect(component).toBeInstanceOf(CreateMawbFromHawbComponent);
		});

		it('should initialize mawb form with required controls', () => {
			expect(component.mawbForm).toBeDefined();
			expect(component.mawbForm.get('mawbPrefix')).toBeTruthy();
			expect(component.mawbForm.get('mawbNumber')).toBeTruthy();
			expect(component.mawbForm.get('chargeableWeight')).toBeTruthy();
			expect(component.mawbForm.get('rateCharge')).toBeTruthy();
		});

		it('should call getCurrencies service on initialization', () => {
			component.ngOnInit();

			expect(sliCreateRequestServiceSpy.getCurrencies).toHaveBeenCalledTimes(1);
		});
	});

	describe('Input Handling', () => {
		it('should accept mawbId input property', () => {
			const testMawbId = 'MAWB-456';
			component.mawbId = testMawbId;

			expect(component.mawbId).toBe(testMawbId);
		});

		it('should fetch organization info when user has valid orgId', () => {
			getCurrentUserSpy.and.returnValue(of(MOCK_USER as any));

			component.ngOnInit();

			expect(orgMgmtRequestServiceSpy.getOrgInfo).toHaveBeenCalledWith(MOCK_USER.primaryOrgId);
			expect(orgMgmtRequestServiceSpy.getOrgInfo).toHaveBeenCalledTimes(1);
		});

		it('should not fetch organization info when user has no orgId', () => {
			getCurrentUserSpy.and.returnValue(of({ ...MOCK_USER, primaryOrgId: null } as any));

			component.ngOnInit();

			expect(orgMgmtRequestServiceSpy.getOrgInfo).not.toHaveBeenCalled();
		});
	});

	describe('Form Validation', () => {
		it('should invalidate form when required fields are empty', () => {
			component.mawbForm.patchValue({
				mawbPrefix: '',
				mawbNumber: '',
				grossWeight: null,
				chargeableWeight: null,
				rateCharge: { currencyUnit: '', numericalValue: null },
				natureAndQuantityOfGoods: '',
				date: null,
				atPlace: '',
				signatureOfShipperOrHisAgent: '',
				signatureOfCarrierOrItsAgent: '',
			});

			expect(component.mawbForm.invalid).toBe(true);
			expect(component.mawbForm.get('mawbPrefix')?.invalid).toBe(true);
			expect(component.mawbForm.get('mawbNumber')?.invalid).toBe(true);
			expect(component.mawbForm.get('grossWeight')?.invalid).toBe(true);
			expect(component.mawbForm.get('chargeableWeight')?.invalid).toBe(true);
			expect(component.mawbForm.get('rateCharge')?.invalid).toBe(true);
		});

		describe('grossWeight validation', () => {
			let grossWeightControl: any;

			beforeEach(() => {
				grossWeightControl = component.mawbForm.get('grossWeight');
			});

			it('should accept valid integer values', () => {
				grossWeightControl.setValue(100);
				expect(grossWeightControl.valid).toBe(true);
			});

			it('should accept valid decimal values with one decimal place', () => {
				grossWeightControl.setValue(100.5);
				expect(grossWeightControl.valid).toBe(true);
			});

			it('should reject decimal values with more than one decimal place', () => {
				grossWeightControl.setValue(100.25);
				expect(grossWeightControl.valid).toBe(false);
			});
		});

		describe('chargeableWeight validation', () => {
			let chargeableWeightControl: any;

			beforeEach(() => {
				chargeableWeightControl = component.mawbForm.get('chargeableWeight');
			});

			it('should accept valid integer values', () => {
				chargeableWeightControl.setValue(100);
				expect(chargeableWeightControl.valid).toBe(true);
			});

			it('should accept valid decimal values with .5', () => {
				chargeableWeightControl.setValue(100.5);
				expect(chargeableWeightControl.valid).toBe(true);
			});

			it('should reject decimal values other than .5', () => {
				chargeableWeightControl.setValue(100.25);
				expect(chargeableWeightControl.valid).toBe(false);
			});
		});

		describe('rateCharge numericalValue validation', () => {
			let rateChargeControl: any;

			beforeEach(() => {
				rateChargeControl = component.mawbForm.get('rateCharge')?.get('numericalValue');
			});

			it('should accept valid integer values', () => {
				rateChargeControl.setValue(100);
				expect(rateChargeControl.valid).toBe(true);
			});

			it('should accept valid decimal values', () => {
				rateChargeControl.setValue(100.5);
				expect(rateChargeControl.valid).toBe(true);
			});
		});
	});

	describe('Form Default Values', () => {
		it('should have default value for natureAndQuantityOfGoods', () => {
			expect(component.mawbForm.get('natureAndQuantityOfGoods')?.value).toBe("Consolidation as per att'd list");
		});

		it('should initialize form with empty values for most fields', () => {
			expect(component.mawbForm.get('mawbPrefix')?.value).toBe('');
			expect(component.mawbForm.get('mawbNumber')?.value).toBe('');
			expect(component.mawbForm.get('grossWeight')?.value).toBeNull();
			expect(component.mawbForm.get('chargeableWeight')?.value).toBeNull();
			expect(component.mawbForm.get('rateCharge')?.get('currencyUnit')?.value).toBe('');
			expect(component.mawbForm.get('rateCharge')?.get('numericalValue')?.value).toBeNull();
		});

		it('should initialize disabled fields correctly', () => {
			expect(component.mawbForm.get('noOfPiecesRcp')?.disabled).toBe(true);
			expect(component.mawbForm.get('total')?.disabled).toBe(true);
			expect(component.mawbForm.get('destinationCollectCharges')?.disabled).toBe(true);
			expect(component.mawbForm.get('totalCollectCharges')?.disabled).toBe(true);
		});

		it('should allow manual form population', () => {
			// Test that form can be manually populated (as would happen in real usage)
			component.mawbForm.patchValue({
				mawbPrefix: 'TEST',
				mawbNumber: '********',
				grossWeight: 100,
				chargeableWeight: 80,
				rateCharge: { currencyUnit: 'USD', numericalValue: 50 },
			});

			expect(component.mawbForm.get('mawbPrefix')?.value).toBe('TEST');
			expect(component.mawbForm.get('mawbNumber')?.value).toBe('********');
			expect(component.mawbForm.get('grossWeight')?.value).toBe(100);
			expect(component.mawbForm.get('chargeableWeight')?.value).toBe(80);
			expect(component.mawbForm.get('rateCharge')?.get('currencyUnit')?.value).toBe('USD');
			expect(component.mawbForm.get('rateCharge')?.get('numericalValue')?.value).toBe(50);
		});
	});

	describe('MAWB Operations', () => {
		beforeEach(() => {
			// Setup valid form data for operations
			component.mawbForm.patchValue({
				mawbPrefix: 'carrier1', // Use valid carrier ID
				mawbNumber: '********', // Valid MAWB number with correct check digit
				grossWeight: 100,
				chargeableWeight: 80,
				rateCharge: { currencyUnit: 'USD', numericalValue: 50 },
				natureAndQuantityOfGoods: 'Test goods',
				date: new Date(),
				atPlace: 'Test place',
				signatureOfShipperOrHisAgent: 'Test signature',
				signatureOfCarrierOrItsAgent: 'Carrier signature',
			});

			// Setup child component forms to be valid
			component.airPortInfoComponent.airportInfoForm.patchValue({
				departureAndRequestedRouting: 'LAX',
				airportOfDestination: 'JFK',
			});
			component.carrierAgentComponent.carrierAgentForm.setErrors(null);

			// Mock form validation to pass by setting forms as valid
			Object.defineProperty(component.mawbForm, 'invalid', { value: false, configurable: true });
			Object.defineProperty(component.airPortInfoComponent.airportInfoForm, 'invalid', { value: false, configurable: true });
			Object.defineProperty(component.carrierAgentComponent.carrierAgentForm, 'invalid', { value: false, configurable: true });

			// Mock getFormData methods to return valid data
			component.shipperOrConsigneeInfoComponentList.first.getFormData = jasmine.createSpy('getFormData').and.returnValue({
				companyType: 'SHIPPER',
				companyName: 'Test Shipper',
			});
			component.shipperOrConsigneeInfoComponentList.last.getFormData = jasmine.createSpy('getFormData').and.returnValue({
				companyType: 'CONSIGNEE',
				companyName: 'Test Consignee',
			});
			component.carrierAgentComponent.getFormData = jasmine.createSpy('getFormData').and.returnValue({
				companyType: 'CARRIER',
				companyName: 'Test Carrier',
			});
			component.issuedByComponent.getData = jasmine.createSpy('getData').and.returnValue({
				companyType: 'ISSUER',
				companyName: 'Test Issuer',
			});
		});

		it('should create MAWB successfully when no mawbId exists', () => {
			component.mawbId = undefined;

			component.onSave();

			expect(mawbCreateRequestServiceSpy.createMawb).toHaveBeenCalledTimes(1);
			expect(notificationServiceSpy.showSuccess).toHaveBeenCalled();
			expect(routerSpy.navigate).toHaveBeenCalledWith(['/mawb']);
		});

		it('should update MAWB successfully when hawbId and orgId exist', () => {
			component.mawbId = 'MAWB-456';
			component['orgId'] = 'ORG-123';

			component.onSave();

			expect(mawbCreateRequestServiceSpy.updateMawb).toHaveBeenCalledWith('MAWB-456', 'ORG-123', jasmine.any(Object));
			expect(notificationServiceSpy.showSuccess).toHaveBeenCalled();
			expect(routerSpy.navigate).toHaveBeenCalledWith(['/mawb']);
		});

		it('should handle form validation before saving', () => {
			// Reset form to invalid state
			component.mawbForm.patchValue({
				mawbPrefix: '', // Invalid - required field
				mawbNumber: '',
			});
			Object.defineProperty(component.mawbForm, 'valid', { value: false, configurable: true });

			component.onSave();

			// Should mark forms as touched for validation display
			expect(component.mawbForm.markAllAsTouched).toBeDefined();
		});
	});

	describe('onWtOrValChange', () => {
		it('should update prepaid form when wtOrVal is PREPAID', () => {
			const mockEvent = { value: DropDownType.PREPAID } as any;
			component.mawbForm.patchValue({ total: 150 });
			component.airPortInfoComponent.airportInfoForm.get('declaredValueForCarriage')?.patchValue({
				currencyUnit: 'USD',
				numericalValue: '200',
			});

			component.onWtOrValChange(mockEvent);

			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				weightChargePrepaid: 150,
				weightChargeCollect: null,
				valuationChargePrepaid: 200,
				valuationChargeCollect: null,
			});
		});

		it('should update collect form when wtOrVal is COLLECT', () => {
			const mockEvent = { value: DropDownType.COLLECT } as any;
			component.mawbForm.patchValue({ total: 150 });
			component.airPortInfoComponent.airportInfoForm.get('declaredValueForCarriage')?.patchValue({
				currencyUnit: 'USD',
				numericalValue: '200',
			});

			component.onWtOrValChange(mockEvent);

			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				weightChargePrepaid: null,
				weightChargeCollect: 150,
				valuationChargePrepaid: null,
				valuationChargeCollect: 200,
			});
		});
	});

	describe('onOtherChargesChange', () => {
		it('should handle prepaid charges correctly', () => {
			const mockCharges: EmitCharges[] = [
				{
					chargePaymentType: DropDownType.PREPAID,
					taxCharges: 10,
					agentCharges: 20,
					carrierCharges: 30,
				},
			];

			component.onOtherChargesChange(mockCharges);

			// The method makes multiple calls: first clears collect fields (since no collect charges), then sets prepaid fields
			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				taxCollect: null,
				totalOtherChargesDueAgentCollect: null,
				totalOtherChargesDueCarrierCollect: null,
			});
			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				taxPrepaid: 10,
				totalOtherChargesDueAgentPrepaid: 20,
				totalOtherChargesDueCarrierPrepaid: 30,
			});
		});

		it('should handle collect charges correctly', () => {
			const mockCharges: EmitCharges[] = [
				{
					chargePaymentType: DropDownType.COLLECT,
					taxCharges: 15,
					agentCharges: 25,
					carrierCharges: 35,
				},
			];

			component.onOtherChargesChange(mockCharges);

			// The method makes multiple calls: first clears prepaid fields (since no prepaid charges), then sets collect fields
			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				taxPrepaid: null,
				totalOtherChargesDueAgentPrepaid: null,
				totalOtherChargesDueCarrierPrepaid: null,
			});
			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				taxCollect: 15,
				totalOtherChargesDueAgentCollect: 25,
				totalOtherChargesDueCarrierCollect: 35,
			});
		});

		it('should clear prepaid values when no prepaid charges', () => {
			const mockCharges = [{ chargePaymentType: 'Collect', taxCharges: 15, agentCharges: 25, carrierCharges: 35 }];

			component.onOtherChargesChange(mockCharges as any);

			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				taxPrepaid: null,
				totalOtherChargesDueAgentPrepaid: null,
				totalOtherChargesDueCarrierPrepaid: null,
			});
		});
	});

	describe('alsoNotifies management', () => {
		beforeEach(() => {
			component.alsoNotifies = [{ companyName: 'Company 1' } as any, { companyName: 'Company 2' } as any];
		});

		it('should delete alsoNotify item at specified index', () => {
			const mockEvent = { preventDefault: jasmine.createSpy(), stopPropagation: jasmine.createSpy() } as any;

			component.delAlsoNotify(0, mockEvent);

			expect(mockEvent.preventDefault).toHaveBeenCalled();
			expect(mockEvent.stopPropagation).toHaveBeenCalled();
			expect(component.alsoNotifies.length).toBe(1);
			expect(component.alsoNotifies[0].companyName).toBe('Company 2');
		});

		it('should add new alsoNotify item', () => {
			const initialLength = component.alsoNotifies.length;

			component.addAlsoNotify();

			expect(component.alsoNotifies.length).toBe(initialLength + 1);
			expect(component.alsoNotifies[component.alsoNotifies.length - 1]).toEqual(
				jasmine.objectContaining({
					companyName: '',
					contactName: '',
					countryCode: '',
					regionCode: '',
					cityCode: '',
					locationName: '',
					textualPostCode: '',
					phoneNumber: '',
					emailAddress: '',
				})
			);
		});
	});

	describe('onCancel', () => {
		it('should open confirm dialog and navigate on confirmation', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(true));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			component.onCancel();

			expect(component['dialog'].open).toHaveBeenCalled();
			expect(routerSpy.navigate).toHaveBeenCalledWith(['/mawb/create']);
			expect(component.isConfirmed).toBeTruthy();
		});

		it('should not navigate when dialog is cancelled', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(false));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			component.onCancel();

			expect(component['dialog'].open).toHaveBeenCalled();
			expect(routerSpy.navigate).not.toHaveBeenCalled();
			expect(component.isConfirmed).toBeFalsy();
		});
	});

	describe('markFormGroupTouched', () => {
		it('should mark all form controls as touched', () => {
			const formGroup = new FormGroup({
				control1: new FormControl(''),
				control2: new FormControl(''),
				nestedGroup: new FormGroup({
					nestedControl: new FormControl(''),
				}),
			});

			spyOn(formGroup.get('control1')!, 'markAsTouched');
			spyOn(formGroup.get('control2')!, 'markAsTouched');
			spyOn(formGroup.get('nestedGroup')!, 'markAsTouched');

			(component as any).markFormGroupTouched(formGroup);

			expect(formGroup.get('control1')!.markAsTouched).toHaveBeenCalled();
			expect(formGroup.get('control2')!.markAsTouched).toHaveBeenCalled();
			expect(formGroup.get('nestedGroup')!.markAsTouched).toHaveBeenCalled();
		});
	});

	describe('onSave', () => {
		beforeEach(() => {
			// Setup valid form data
			component.mawbForm.patchValue({
				mawbPrefix: 'ABC',
				mawbNumber: '123456',
				grossWeight: 100,
				chargeableWeight: 100,
				rateCharge: { currencyUnit: 'USD', numericalValue: 50 },
				natureAndQuantityOfGoods: 'Test goods',
				date: new Date(),
				atPlace: 'Test place',
				signatureOfShipperOrHisAgent: 'Shipper signature',
				signatureOfCarrierOrItsAgent: 'Carrier signature',
			});

			// Mock child component forms as valid
			component.airPortInfoComponent.airportInfoForm.setErrors(null);
			component.carrierAgentComponent.carrierAgentForm.setErrors(null);
			component.otherChargesComponent.otherChargesForm.setErrors(null);
			component.prepaidCollectComponent.prepaidForm.setErrors(null);

			// Mock child component form values
			component.airPortInfoComponent.airportInfoForm.patchValue({
				departureAndRequestedRouting: 'JFK',
				airportOfDestination: 'LAX',
				declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: '1000' },
				declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: '1000' },
				wtOrVal: 'PREPAID',
			});

			component.carrierAgentComponent.carrierAgentForm.patchValue({
				company: 'Test Carrier',
				agentIataCode: 'TC123',
				country: 'US',
				province: 'NY',
				cityCode: 'New York',
				address: '123 Test St',
				textualPostCode: '10001',
			});
		});

		it('should show error when mawbForm is invalid', () => {
			component.mawbForm.patchValue({ mawbPrefix: '' }); // Make form invalid
			spyOn(component['dialog'], 'open');

			component.onSave();

			expect(component['dialog'].open).toHaveBeenCalled();
		});

		it('should show error when airportInfoForm is invalid', () => {
			component.airPortInfoComponent.airportInfoForm.setErrors({ invalid: true });
			spyOn(component['dialog'], 'open');

			component.onSave();

			expect(component['dialog'].open).toHaveBeenCalled();
		});

		it('should show error when carrierAgentForm is invalid', () => {
			component.carrierAgentComponent.carrierAgentForm.setErrors({ invalid: true });
			spyOn(component['dialog'], 'open');

			component.onSave();

			expect(component['dialog'].open).toHaveBeenCalled();
		});

		it('should update MAWB when mawbId and orgId is provided', () => {
			component.mawbId = 'MAWB-456';
			component['orgId'] = 'ORG-101';
			mawbCreateRequestServiceSpy.updateMawb.and.returnValue(of({}));

			// Mock form validation to pass
			Object.defineProperty(component.mawbForm, 'invalid', { value: false, configurable: true });
			Object.defineProperty(component.airPortInfoComponent.airportInfoForm, 'invalid', { value: false, configurable: true });
			Object.defineProperty(component.carrierAgentComponent.carrierAgentForm, 'invalid', { value: false, configurable: true });

			// Mock getFormData methods to return valid data
			component.shipperOrConsigneeInfoComponentList.first.getFormData = jasmine.createSpy('getFormData').and.returnValue({
				companyType: 'SHIPPER',
				companyName: 'Test Shipper',
			});
			component.shipperOrConsigneeInfoComponentList.last.getFormData = jasmine.createSpy('getFormData').and.returnValue({
				companyType: 'CONSIGNEE',
				companyName: 'Test Consignee',
			});
			component.carrierAgentComponent.getFormData = jasmine.createSpy('getFormData').and.returnValue({
				companyType: 'CARRIER',
				companyName: 'Test Carrier',
			});
			component.issuedByComponent.getData = jasmine.createSpy('getData').and.returnValue({
				companyType: 'ISSUER',
				companyName: 'Test Issuer',
			});

			component.onSave();

			expect(mawbCreateRequestServiceSpy.updateMawb).toHaveBeenCalledWith('MAWB-456', 'ORG-101', jasmine.any(Object));
			expect(notificationServiceSpy.showSuccess).toHaveBeenCalled();
			expect(routerSpy.navigate).toHaveBeenCalledWith(['/mawb']);
		});
	});

	describe('ngAfterViewInit', () => {
		it('should setup form value change subscriptions', () => {
			spyOn(component.mawbForm.get('total')!.valueChanges, 'pipe').and.returnValue(of(100));
			spyOn(
				component.airPortInfoComponent.airportInfoForm.get('declaredValueForCarriage')!.get('numericalValue')!.valueChanges,
				'pipe'
			).and.returnValue(of('50'));

			component.ngAfterViewInit();

			expect(component.mawbForm.get('total')!.valueChanges.pipe).toHaveBeenCalled();
			expect(
				component.airPortInfoComponent.airportInfoForm.get('declaredValueForCarriage')!.get('numericalValue')!.valueChanges.pipe
			).toHaveBeenCalled();
		});
	});

	describe('Error handling', () => {
		it('should handle service errors gracefully', () => {
			sliCreateRequestServiceSpy.getCurrencies.and.returnValue(of([]));
			sliCreateRequestServiceSpy.getSliDetail.and.returnValue(
				of({
					shipmentParty: [],
					rateCharge: {
						currencyUnit: 'USD',
						numericalValue: 0,
					},
					waybillPrefix: '',
					waybillNumber: '',
					accountingInformation: '',
					rateClassCode: '',
					totalVolumetricWeight: 0,
					goodsDescription: '',
					carrierDeclarationDate: new Date().toISOString(),
					carrierDeclarationPlace: '',
					consignorDeclarationSignature: '',
					carrierDeclarationSignature: '',
				} as any)
			);

			expect(() => component.ngOnInit()).not.toThrow();
		});

		it('should handle empty service responses', () => {
			sliCreateRequestServiceSpy.getCurrencies.and.returnValue(of([]));

			component.ngOnInit();

			expect(component.currencies).toEqual(['']);
		});
	});

	describe('mawbNumberValidator', () => {
		it('should return null for empty value', () => {
			const control = { value: '' } as any;
			const result = component.mawbNumberValidator(control);
			expect(result).toBeNull();
		});

		it('should return null for null value', () => {
			const control = { value: null } as any;
			const result = component.mawbNumberValidator(control);
			expect(result).toBeNull();
		});

		it('should return error for non-8-digit number', () => {
			const control = { value: '1234567' } as any;
			const result = component.mawbNumberValidator(control);
			expect(result).toEqual({
				mawbNumberFormat: { message: 'mawb.formItem.mawbNumber.checkLength' },
			});
		});

		it('should return error for non-numeric value', () => {
			const control = { value: '1234567a' } as any;
			const result = component.mawbNumberValidator(control);
			expect(result).toEqual({
				mawbNumberFormat: { message: 'mawb.formItem.mawbNumber.checkLength' },
			});
		});

		it('should return error for incorrect check digit', () => {
			const control = { value: '********' } as any;
			const result = component.mawbNumberValidator(control);
			// 1234567 % 7 = 5, but the 8th digit is 8
			expect(result).toEqual({
				mawbNumberCheckDigit: { message: 'mawb.formItem.mawbNumber.checkDigit5' },
			});
		});

		it('should return null for valid MAWB number', () => {
			const control = { value: '12345675' } as any;
			const result = component.mawbNumberValidator(control);
			// 1234567 % 7 = 5, and the 8th digit is 5
			expect(result).toBeNull();
		});

		it('should validate different check digits correctly', () => {
			// Test case where mod 7 = 6
			const control1 = { value: '12345686' } as any;
			const result1 = component.mawbNumberValidator(control1);
			// 1234568 % 7 = 6, and the 8th digit is 6
			expect(result1).toBeNull();

			// Test case where mod 7 = 0
			const control2 = { value: '12345690' } as any;
			const result2 = component.mawbNumberValidator(control2);
			// 1234569 % 7 = 0, and the 8th digit is 0
			expect(result2).toBeNull();
		});
	});

	describe('onMawbPrefixChange', () => {
		it('should fetch organization info when selectedOrgId is provided', () => {
			const mockEvent = { value: 'test-org-id' } as any;

			component.onMawbPrefixChange(mockEvent);

			expect(orgMgmtRequestServiceSpy.getOrgInfo).toHaveBeenCalledWith('test-org-id');
		});

		it('should update issuedByInfo when organization info is fetched', () => {
			const mockEvent = { value: 'test-org-id' } as any;
			const mockOrgInfo = { id: 'test-org-id', companyName: 'Test Org' } as any;
			orgMgmtRequestServiceSpy.getOrgInfo.and.returnValue(of(mockOrgInfo));

			component.onMawbPrefixChange(mockEvent);

			expect(component.issuedByInfo).toBe(mockOrgInfo);
		});

		it('should not fetch organization info when selectedOrgId is null', () => {
			const mockEvent = { value: null } as any;

			component.onMawbPrefixChange(mockEvent);

			expect(orgMgmtRequestServiceSpy.getOrgInfo).not.toHaveBeenCalled();
		});

		it('should not fetch organization info when selectedOrgId is empty string', () => {
			const mockEvent = { value: '' } as any;

			component.onMawbPrefixChange(mockEvent);

			expect(orgMgmtRequestServiceSpy.getOrgInfo).not.toHaveBeenCalled();
		});
	});

	describe('allPropertySame', () => {
		it('should return true for empty array', () => {
			const result = component.allPropertySame([], 'testProperty');
			expect(result).toBe(true);
		});

		it('should return true when all objects have same property value', () => {
			const testArray = [
				{ testProperty: 'value1', other: 'different1' },
				{ testProperty: 'value1', other: 'different2' },
				{ testProperty: 'value1', other: 'different3' },
			];

			const result = component.allPropertySame(testArray, 'testProperty');
			expect(result).toBe(true);
		});

		it('should return false when objects have different property values', () => {
			const testArray = [
				{ testProperty: 'value1', other: 'same' },
				{ testProperty: 'value2', other: 'same' },
				{ testProperty: 'value1', other: 'same' },
			];

			const result = component.allPropertySame(testArray, 'testProperty');
			expect(result).toBe(false);
		});

		it('should return true for single item array', () => {
			const testArray = [{ testProperty: 'value1' }];

			const result = component.allPropertySame(testArray, 'testProperty');
			expect(result).toBe(true);
		});

		it('should handle null and undefined values correctly', () => {
			const testArray = [{ testProperty: null }, { testProperty: null }, { testProperty: null }];

			const result = component.allPropertySame(testArray, 'testProperty');
			expect(result).toBe(true);
		});
	});

	describe('goBack', () => {
		it('should open confirm dialog with translated message', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(true));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);
			spyOn(component['translateService'], 'instant').and.returnValue('Translated message');

			component.goBack('test.message.key');

			expect(component['dialog'].open).toHaveBeenCalledWith(jasmine.any(Function), {
				width: '300px',
				data: {
					content: 'Translated message',
				},
			});
			expect(component['translateService'].instant).toHaveBeenCalledWith('test.message.key');
		});

		it('should navigate to mawb/create after dialog is closed', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(true));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			component.goBack('test.message.key');

			expect(routerSpy.navigate).toHaveBeenCalledWith(['/mawb/create']);
		});
	});

	describe('getOrgList', () => {
		it('should open SelectOrgDialogComponent with correct configuration', () => {
			const mockEvent = { preventDefault: jasmine.createSpy(), stopPropagation: jasmine.createSpy() } as any;
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(null));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			component.getOrgList(0, mockEvent);

			expect(mockEvent.preventDefault).toHaveBeenCalled();
			expect(mockEvent.stopPropagation).toHaveBeenCalled();
			expect(component['dialog'].open).toHaveBeenCalledWith(jasmine.any(Function), {
				width: '400px',
				data: {
					orgType: '',
				},
			});
		});

		it('should update alsoNotifies when dialog returns result', () => {
			const mockEvent = { preventDefault: jasmine.createSpy(), stopPropagation: jasmine.createSpy() } as any;
			const mockResult = {
				id: 'org-123',
				companyName: 'Test Company',
				countryCode: 'US',
				regionCode: 'CA',
				cityCode: 'LA',
				textualPostCode: '90210',
				locationName: 'Los Angeles',
				persons: [
					{
						contactRole: 'CUSTOMER_CONTACT',
						contactName: 'John Doe',
						phoneNumber: '************',
						emailAddress: '<EMAIL>',
					},
				],
			};
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(mockResult));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			component.alsoNotifies = [{ companyName: 'Old Company' } as any];

			component.getOrgList(0, mockEvent);

			expect(component.alsoNotifies[0]).toEqual(
				jasmine.objectContaining({
					id: 'org-123',
					companyName: 'Test Company',
					contactName: 'John Doe',
					countryCode: 'US',
					regionCode: 'CA',
					cityCode: 'LA',
					textualPostCode: '90210',
					locationName: 'Los Angeles',
					phoneNumber: '************',
					emailAddress: '<EMAIL>',
					companyType: '',
				})
			);
		});

		it('should not update alsoNotifies when dialog is cancelled', () => {
			const mockEvent = { preventDefault: jasmine.createSpy(), stopPropagation: jasmine.createSpy() } as any;
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(null));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			const originalAlsoNotifies = [{ companyName: 'Original Company' } as any];
			component.alsoNotifies = [...originalAlsoNotifies];

			component.getOrgList(0, mockEvent);

			expect(component.alsoNotifies).toEqual(originalAlsoNotifies);
		});
	});

	describe('fillMawbInfo', () => {
		let mockMawbDetail: any;

		beforeEach(() => {
			mockMawbDetail = {
				orgId: 'test-org-id',
				waybillPrefix: 'AA',
				waybillNumber: '********',
				accountingNoteList: [
					{ accountingNoteIdentifier: 'SHP', accountingNoteText: 'Shipper note' },
					{ accountingNoteIdentifier: 'CNE', accountingNoteText: 'Consignee note' },
					{ accountingNoteIdentifier: 'FFW', accountingNoteText: 'Forwarder note' },
					{ accountingNoteIdentifier: 'Account Information', accountingNoteText: 'Carrier note' },
				],
				partyList: [
					{ companyType: 'SHP', companyName: 'Test Shipper', id: 'shipper-1' },
					{ companyType: 'CNE', companyName: 'Test Consignee', id: 'consignee-1' },
					{ companyType: 'FFW', companyName: 'Test Forwarder', id: 'forwarder-1' },
					{ companyType: 'AIR', companyName: 'Test Carrier', id: 'carrier-1', airlineCode: 'AA' },
					{ companyName: 'Also Notify 1', id: 'notify-1' },
				],
				departureLocation: 'LAX',
				arrivalLocation: 'JFK',
				requestedFlight: 'AA123',
				requestedDate: '2023-01-01T10:00:00Z',
				toFirst: 'JFK',
				toSecond: 'LHR',
				toThird: 'CDG',
				byFirstCarrier: 'AA',
				bySecondCarrier: 'BA',
				byThirdCarrier: 'AF',
				insuredAmount: { id: 'ins-1', currencyUnit: 'USD', numericalValue: 1000 },
				carrierChargeCode: 'CC',
				weightValuationIndicator: 'PREPAID',
				otherChargesIndicator: 'COLLECT',
				declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: 500 },
				declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: 400 },
				textualHandlingInstructions: 'Handle with care',
				totalGrossWeight: 100.5,
				serviceCode: 'N',
				rateClassCode: 'N',
				totalVolumetricWeight: 80.5,
				rateCharge: { currencyUnit: 'USD', numericalValue: 50.25 },
				goodsDescription: 'Test goods',
				otherChargeList: [{ chargeCode: 'CC', amount: 10 }],
				destinationCurrencyRate: 1.2,
				destinationCharges: { currencyUnit: 'EUR', numericalValue: 25.5 },
				shippingInfo: 'Test shipping info',
				shippingRefNo: 'REF123',
				carrierDeclarationDate: '2023-01-01T12:00:00Z',
				carrierDeclarationPlace: 'Los Angeles',
				consignorDeclarationSignature: 'Shipper Signature',
				carrierDeclarationSignature: 'Carrier Signature',
			};

			// Setup mock components with proper form structure
			component.shipperOrConsigneeInfoComponentList = {
				first: {
					shipperConsigneeForm: {
						patchValue: jasmine.createSpy('patchValue'),
					},
				},
				last: {
					shipperConsigneeForm: {
						patchValue: jasmine.createSpy('patchValue'),
					},
				},
			} as any;

			component.carrierAgentComponent = {
				carrierAgentForm: {
					patchValue: jasmine.createSpy('patchValue'),
				},
			} as any;

			component.airPortInfoComponent = {
				airportInfoForm: {
					patchValue: jasmine.createSpy('patchValue'),
				},
			} as any;

			component.otherChargesComponent = {
				initOtherCharges: jasmine.createSpy('initOtherCharges'),
			} as any;

			component.carriers = [
				{ id: 'carrier-1', prefix: 'AA', name: 'American Airlines' },
				{ id: 'carrier-2', prefix: 'DL', name: 'Delta Airlines' },
			] as any;
		});

		it('should populate component properties from mawbDetail', () => {
			component.fillMawbInfo(mockMawbDetail);

			expect(component['orgId']).toBe('test-org-id');
			expect(component.shipperInfo).toEqual(jasmine.objectContaining({ companyType: 'SHP', companyName: 'Test Shipper' }));
			expect(component.consigneeInfo).toEqual(jasmine.objectContaining({ companyType: 'CNE', companyName: 'Test Consignee' }));
			expect(component.carrierInfo).toEqual(jasmine.objectContaining({ companyType: 'FFW', companyName: 'Test Forwarder' }));
			expect(component.alsoNotifies).toEqual([jasmine.objectContaining({ companyName: 'Also Notify 1' })]);
		});

		it('should populate issuedByInfo from carrier party', () => {
			component.fillMawbInfo(mockMawbDetail);

			expect(component.issuedByInfo).toEqual(
				jasmine.objectContaining({
					id: 'carrier-1',
					companyName: 'Test Carrier',
					airlineCode: 'AA',
					partyRole: '',
					persons: [],
				})
			);
		});

		it('should patch shipper and consignee forms with accounting notes', () => {
			component.fillMawbInfo(mockMawbDetail);

			expect(component.shipperOrConsigneeInfoComponentList.first.shipperConsigneeForm.patchValue).toHaveBeenCalledWith({
				accountingNoteText: 'Shipper note',
			});
			expect(component.shipperOrConsigneeInfoComponentList.last.shipperConsigneeForm.patchValue).toHaveBeenCalledWith({
				accountingNoteText: 'Consignee note',
			});
		});

		it('should patch carrier agent form with accounting note', () => {
			component.fillMawbInfo(mockMawbDetail);

			expect(component.carrierAgentComponent.carrierAgentForm.patchValue).toHaveBeenCalledWith({
				accountingNoteText: 'Forwarder note',
			});
		});

		it('should patch airport info form with correct values', () => {
			component.fillMawbInfo(mockMawbDetail);

			expect(component.airPortInfoComponent.airportInfoForm.patchValue).toHaveBeenCalledWith(
				jasmine.objectContaining({
					departureAndRequestedRouting: 'LAX',
					airportOfDestination: 'JFK',
					flight: 'AA123',
					to: 'JFK',
					toBy2ndCarrier: 'LHR',
					toBy3rdCarrier: 'CDG',
					byFirstCarrier: 'AA',
					by2ndCarrier: 'BA',
					by3rdCarrier: 'AF',
					chargesCode: 'CC',
					wtOrVal: 'PREPAID',
					other: 'COLLECT',
				})
			);
		});

		it('should initialize other charges component', () => {
			component.fillMawbInfo(mockMawbDetail);

			expect(component.otherChargesComponent.initOtherCharges).toHaveBeenCalledWith(mockMawbDetail.otherChargeList);
		});

		it('should patch main form with correct values', () => {
			spyOn(component.mawbForm, 'patchValue');

			component.fillMawbInfo(mockMawbDetail);

			expect(component.mawbForm.patchValue).toHaveBeenCalledWith(
				jasmine.objectContaining({
					mawbPrefix: 'carrier-1',
					mawbNumber: '********',
					accountingInformation: 'Carrier note',
					handingInformation: 'Handle with care',
					grossWeight: 100.5,
					serviceCode: 'N',
					rateClass: 'N',
					chargeableWeight: 80.5,
					rateCharge: {
						currencyUnit: 'USD',
						numericalValue: 50.25,
					},
					natureAndQuantityOfGoods: 'Test goods',
					destinationCurrencyRate: 1.2,
					destinationCharges: {
						currencyUnit: 'EUR',
						numericalValue: 25.5,
					},
					shippingInfo: 'Test shipping info',
					shippingRefNo: 'REF123',
					atPlace: 'Los Angeles',
					signatureOfShipperOrHisAgent: 'Shipper Signature',
					signatureOfCarrierOrItsAgent: 'Carrier Signature',
				})
			);
		});

		it('should handle missing accounting notes gracefully', () => {
			mockMawbDetail.accountingNoteList = [];

			expect(() => component.fillMawbInfo(mockMawbDetail)).not.toThrow();

			expect(component.shipperOrConsigneeInfoComponentList.first.shipperConsigneeForm.patchValue).toHaveBeenCalledWith({
				accountingNoteText: '',
			});
		});

		it('should handle missing party information gracefully', () => {
			mockMawbDetail.partyList = [];

			expect(() => component.fillMawbInfo(mockMawbDetail)).not.toThrow();

			expect(component.shipperInfo).toBeNull();
			expect(component.consigneeInfo).toBeNull();
			expect(component.carrierInfo).toBeNull();
			expect(component.alsoNotifies).toEqual([]);
		});
	});

	describe('ngOnInit - HAWB Detail Processing', () => {
		beforeEach(() => {
			component.mawbId = undefined; // Test creation mode
			component.hawbList = [{ hawbId: 'hawb-1' }, { hawbId: 'hawb-2' }] as any;
		});

		it('should call getHawbDetailBatch when not in edit mode', () => {
			component.ngOnInit();

			expect(mawbCreateRequestServiceSpy.getHawbDetailBatch).toHaveBeenCalledWith(['hawb-1', 'hawb-2']);
		});

		it('should call getMawbDetail when in edit mode', () => {
			component.mawbId = 'mawb-123';

			component.ngOnInit();

			expect(mawbCreateRequestServiceSpy.getMawbDetail).toHaveBeenCalledWith('mawb-123');
		});

		it('should process hawb details and validate weight valuation indicators', () => {
			const mockHawbDetails = [
				{
					weightValuationIndicator: 'PREPAID',
					otherChargesIndicator: 'PREPAID',
					textualHandlingInstructions: 'Handle 1',
					totalGrossWeight: 50,
					otherChargeList: [],
				},
				{
					weightValuationIndicator: 'PREPAID',
					otherChargesIndicator: 'PREPAID',
					textualHandlingInstructions: 'Handle 2',
					totalGrossWeight: 75,
					otherChargeList: [],
				},
			] as any;
			mawbCreateRequestServiceSpy.getHawbDetailBatch.and.returnValue(of(mockHawbDetails));
			getCurrentUserSpy.and.returnValue(of({ primaryOrgId: 'user-org' }));

			component.ngOnInit();

			expect(component.airPortInfoComponent.airportInfoForm.patchValue).toHaveBeenCalledWith({
				wtOrVal: 'PREPAID',
			});
			expect(component.airPortInfoComponent.airportInfoForm.patchValue).toHaveBeenCalledWith({
				other: 'PREPAID',
			});
		});

		it('should call goBack when weight valuation indicators are different', () => {
			const mockHawbDetails = [
				{
					weightValuationIndicator: 'PREPAID',
					otherChargesIndicator: 'PREPAID',
					textualHandlingInstructions: 'Handle 1',
					totalGrossWeight: 50,
					otherChargeList: [],
				},
				{
					weightValuationIndicator: 'COLLECT',
					otherChargesIndicator: 'PREPAID',
					textualHandlingInstructions: 'Handle 2',
					totalGrossWeight: 75,
					otherChargeList: [],
				},
			] as any;
			mawbCreateRequestServiceSpy.getHawbDetailBatch.and.returnValue(of(mockHawbDetails));
			spyOn(component, 'goBack');

			component.ngOnInit();

			expect(component.goBack).toHaveBeenCalledWith('mawb.dialog.weight.validate');
		});

		it('should call goBack when other charges indicators are different', () => {
			const mockHawbDetails = [
				{
					weightValuationIndicator: 'PREPAID',
					otherChargesIndicator: 'PREPAID',
					textualHandlingInstructions: 'Handle 1',
					totalGrossWeight: 50,
					otherChargeList: [],
				},
				{
					weightValuationIndicator: 'PREPAID',
					otherChargesIndicator: 'COLLECT',
					textualHandlingInstructions: 'Handle 2',
					totalGrossWeight: 75,
					otherChargeList: [],
				},
			] as any;
			mawbCreateRequestServiceSpy.getHawbDetailBatch.and.returnValue(of(mockHawbDetails));
			spyOn(component, 'goBack');

			component.ngOnInit();

			expect(component.goBack).toHaveBeenCalledWith('mawb.dialog.other.validate');
		});

		it('should calculate total gross weight from hawb details', () => {
			const mockHawbDetails = [
				{
					weightValuationIndicator: 'PREPAID',
					otherChargesIndicator: 'PREPAID',
					textualHandlingInstructions: 'Handle 1',
					totalGrossWeight: 50.5,
					otherChargeList: [],
				},
				{
					weightValuationIndicator: 'PREPAID',
					otherChargesIndicator: 'PREPAID',
					textualHandlingInstructions: 'Handle 2',
					totalGrossWeight: 75.3,
					otherChargeList: [],
				},
			] as any;
			mawbCreateRequestServiceSpy.getHawbDetailBatch.and.returnValue(of(mockHawbDetails));
			getCurrentUserSpy.and.returnValue(of({ primaryOrgId: 'user-org' }));
			spyOn(component.mawbForm, 'patchValue');

			component.ngOnInit();

			expect(component.mawbForm.patchValue).toHaveBeenCalledWith(
				jasmine.objectContaining({
					handingInformation: 'Handle 1,Handle 2',
					grossWeight: 125.8,
				})
			);
		});

		it('should aggregate other charges from hawb details', () => {
			const mockHawbDetails = [
				{
					weightValuationIndicator: 'PREPAID',
					otherChargesIndicator: 'PREPAID',
					textualHandlingInstructions: 'Handle 1',
					totalGrossWeight: 50,
					otherChargeList: [
						{ chargeCode: 'CC1', amount: 10 },
						{ chargeCode: 'CC2', amount: 20 },
					],
				},
				{
					weightValuationIndicator: 'PREPAID',
					otherChargesIndicator: 'PREPAID',
					textualHandlingInstructions: 'Handle 2',
					totalGrossWeight: 75,
					otherChargeList: [{ chargeCode: 'CC3', amount: 15 }],
				},
			] as any;
			mawbCreateRequestServiceSpy.getHawbDetailBatch.and.returnValue(of(mockHawbDetails));
			getCurrentUserSpy.and.returnValue(of({ primaryOrgId: 'user-org' }));

			component.ngOnInit();

			expect(component.otherChargesList).toEqual(
				jasmine.arrayContaining([
					jasmine.objectContaining({ disabled: true }),
					jasmine.objectContaining({ disabled: true }),
					jasmine.objectContaining({ disabled: true }),
				])
			);
		});

		it('should return early when hawbDetailList is empty', () => {
			mawbCreateRequestServiceSpy.getHawbDetailBatch.and.returnValue(of([]));
			getCurrentUserSpy.and.returnValue(of({ primaryOrgId: null }));
			spyOn(component.mawbForm, 'patchValue');

			component.ngOnInit();

			// The form will still be patched with subscription-based values, but not with hawb data
			expect(component.mawbForm.patchValue).toHaveBeenCalledWith({ total: 0 });
			expect(component.mawbForm.patchValue).toHaveBeenCalledWith({ destinationCollectCharges: null });
			expect(component.mawbForm.patchValue).toHaveBeenCalledWith({ totalCollectCharges: null });
		});

		it('should fetch user organization info when user has primaryOrgId', () => {
			const mockHawbDetails = [
				{
					weightValuationIndicator: 'PREPAID',
					otherChargesIndicator: 'PREPAID',
					textualHandlingInstructions: 'Handle 1',
					totalGrossWeight: 50,
					otherChargeList: [],
				},
			] as any;
			mawbCreateRequestServiceSpy.getHawbDetailBatch.and.returnValue(of(mockHawbDetails));
			getCurrentUserSpy.and.returnValue(of({ primaryOrgId: 'user-org-123' }));

			component.ngOnInit();

			expect(orgMgmtRequestServiceSpy.getOrgInfo).toHaveBeenCalledWith('user-org-123');
		});
	});

	describe('Form Value Change Subscriptions', () => {
		it('should setup chargeableWeight and rateCharge value change subscription', () => {
			const chargeableWeightControl = component.mawbForm.get('chargeableWeight')!;
			const rateChargeControl = component.mawbForm.get('rateCharge')!.get('numericalValue')!;
			spyOn(component.mawbForm, 'patchValue');

			component.ngOnInit();

			// Simulate value changes
			chargeableWeightControl.setValue(100);
			rateChargeControl.setValue(50);

			expect(component.mawbForm.patchValue).toHaveBeenCalledWith({
				total: 5000,
			});
		});

		it('should calculate total as 0 when chargeableWeight or rateCharge is invalid', () => {
			const chargeableWeightControl = component.mawbForm.get('chargeableWeight')!;
			const rateChargeControl = component.mawbForm.get('rateCharge')!.get('numericalValue')!;
			spyOn(component.mawbForm, 'patchValue');

			component.ngOnInit();

			// Simulate invalid values
			chargeableWeightControl.setValue(null);
			rateChargeControl.setValue(50);

			expect(component.mawbForm.patchValue).toHaveBeenCalledWith({
				total: 0,
			});
		});

		it('should setup destinationCurrencyRate value change subscription', () => {
			const destinationCurrencyRateControl = component.mawbForm.get('destinationCurrencyRate')!;
			spyOn(component.mawbForm, 'patchValue');

			// Mock prepaidCollectComponent form
			component.prepaidCollectComponent = {
				prepaidForm: {
					get: jasmine.createSpy('get').and.returnValue({ value: 100 }),
				},
			} as any;

			component.ngOnInit();

			// Simulate value change
			destinationCurrencyRateControl.setValue(1.5);

			expect(component.mawbForm.patchValue).toHaveBeenCalledWith({
				destinationCollectCharges: 150,
			});
		});

		it('should setup destinationCharges numericalValue change subscription', () => {
			const destinationChargesControl = component.mawbForm.get('destinationCharges')!.get('numericalValue')!;
			spyOn(component.mawbForm, 'patchValue');

			// Mock destinationCollectCharges control
			const mockDestinationCollectChargesControl = { value: 50 };
			spyOn(component.mawbForm, 'get').and.callFake((controlName: string) => {
				if (controlName === 'destinationCollectCharges') {
					return mockDestinationCollectChargesControl as any;
				}
				return (component.mawbForm.controls as any)[controlName] || null;
			});

			component.ngOnInit();

			// Simulate value change
			destinationChargesControl.setValue(25);

			expect(component.mawbForm.patchValue).toHaveBeenCalledWith({
				totalCollectCharges: 75,
			});
		});
	});

	describe('ngAfterViewInit - Form Value Change Subscriptions', () => {
		beforeEach(() => {
			// Setup mock components with proper form structure
			component.airPortInfoComponent = {
				airportInfoForm: {
					get: jasmine.createSpy('get').and.returnValue({
						valueChanges: of('PREPAID'),
						value: 'PREPAID',
					}),
				},
			} as any;

			component.prepaidCollectComponent = {
				prepaidForm: {
					patchValue: jasmine.createSpy('patchValue'),
				},
			} as any;

			component.otherChargesComponent = {
				otherChargesForm: {
					patchValue: jasmine.createSpy('patchValue'),
				},
				initOtherCharges: jasmine.createSpy('initOtherCharges'),
			} as any;
		});

		it('should setup total value change subscription for weight charges', () => {
			const totalControl = component.mawbForm.get('total')!;
			spyOn(totalControl.valueChanges, 'pipe').and.returnValue(of(150));

			// Mock the nested form structure properly
			const mockDeclaredValueControl = {
				valueChanges: of('200'),
				pipe: jasmine.createSpy('pipe').and.returnValue(of('200')),
			};

			const mockOtherControl = {
				valueChanges: of('PREPAID'),
				pipe: jasmine.createSpy('pipe').and.returnValue(of('PREPAID')),
			};

			component.airPortInfoComponent.airportInfoForm.get = jasmine.createSpy('get').and.callFake((path: string) => {
				if (path === 'wtOrVal') {
					return { value: DropDownType.PREPAID };
				} else if (path === 'declaredValueForCarriage') {
					return {
						get: jasmine.createSpy('get').and.returnValue(mockDeclaredValueControl),
					};
				} else if (path === 'other') {
					return mockOtherControl;
				}
				return null;
			});

			component.ngAfterViewInit();

			expect(totalControl.valueChanges.pipe).toHaveBeenCalled();
		});

		it('should update prepaid weight charge when total changes and wtOrVal is PREPAID', () => {
			const totalControl = component.mawbForm.get('total')!;
			spyOn(totalControl.valueChanges, 'pipe').and.returnValue(of(150));

			// Mock the nested form structure properly
			const mockDeclaredValueControl = {
				valueChanges: of('200'),
				pipe: jasmine.createSpy('pipe').and.returnValue(of('200')),
			};

			const mockOtherControl = {
				valueChanges: of('PREPAID'),
				pipe: jasmine.createSpy('pipe').and.returnValue(of('PREPAID')),
			};

			component.airPortInfoComponent.airportInfoForm.get = jasmine.createSpy('get').and.callFake((path: string) => {
				if (path === 'wtOrVal') {
					return { value: DropDownType.PREPAID };
				} else if (path === 'declaredValueForCarriage') {
					return {
						get: jasmine.createSpy('get').and.returnValue(mockDeclaredValueControl),
					};
				} else if (path === 'other') {
					return mockOtherControl;
				}
				return null;
			});

			component.ngAfterViewInit();

			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				weightChargePrepaid: 150,
			});
		});

		it('should update collect weight charge when total changes and wtOrVal is not PREPAID', () => {
			const totalControl = component.mawbForm.get('total')!;
			spyOn(totalControl.valueChanges, 'pipe').and.returnValue(of(150));

			// Mock the nested form structure properly
			const mockDeclaredValueControl = {
				valueChanges: of('200'),
				pipe: jasmine.createSpy('pipe').and.returnValue(of('200')),
			};

			const mockOtherControl = {
				valueChanges: of('COLLECT'),
				pipe: jasmine.createSpy('pipe').and.returnValue(of('COLLECT')),
			};

			component.airPortInfoComponent.airportInfoForm.get = jasmine.createSpy('get').and.callFake((path: string) => {
				if (path === 'wtOrVal') {
					return { value: DropDownType.COLLECT };
				} else if (path === 'declaredValueForCarriage') {
					return {
						get: jasmine.createSpy('get').and.returnValue(mockDeclaredValueControl),
					};
				} else if (path === 'other') {
					return mockOtherControl;
				}
				return null;
			});

			component.ngAfterViewInit();

			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				weightChargeCollect: 150,
			});
		});

		it('should setup declaredValueForCarriage numericalValue change subscription', () => {
			const mockValueChanges = {
				pipe: jasmine.createSpy('pipe').and.returnValue({
					subscribe: jasmine.createSpy('subscribe'),
				}),
			};

			const mockDeclaredValueControl = {
				valueChanges: mockValueChanges,
			};

			component.airPortInfoComponent.airportInfoForm.get = jasmine.createSpy('get').and.callFake((path: string) => {
				if (path === 'declaredValueForCarriage') {
					return {
						get: jasmine.createSpy('get').and.returnValue(mockDeclaredValueControl),
					};
				}
				if (path === 'wtOrVal') {
					return { value: DropDownType.PREPAID };
				}
				return null;
			});

			component.ngAfterViewInit();

			expect(mockValueChanges.pipe).toHaveBeenCalled();
		});

		it('should update prepaid valuation charge when declaredValueForCarriage changes and wtOrVal is PREPAID', () => {
			const mockSubscribeCallback = jasmine.createSpy('subscribe');
			const mockDeclaredValueControl = {
				valueChanges: {
					pipe: jasmine.createSpy('pipe').and.returnValue({
						subscribe: mockSubscribeCallback,
					}),
				},
			};

			component.airPortInfoComponent.airportInfoForm.get = jasmine.createSpy('get').and.callFake((path: string) => {
				if (path === 'declaredValueForCarriage') {
					return {
						get: jasmine.createSpy('get').and.returnValue(mockDeclaredValueControl),
					};
				}
				if (path === 'wtOrVal') {
					return { value: DropDownType.PREPAID };
				}
				return null;
			});

			component.ngAfterViewInit();

			// Simulate the subscription callback being called with a value
			const subscriptionCallback = mockSubscribeCallback.calls.mostRecent().args[0];
			subscriptionCallback('200.50');

			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				valuationChargePrepaid: 200.5,
			});
		});

		it('should update collect valuation charge when declaredValueForCarriage changes and wtOrVal is not PREPAID', () => {
			const mockSubscribeCallback = jasmine.createSpy('subscribe');
			const mockDeclaredValueControl = {
				valueChanges: {
					pipe: jasmine.createSpy('pipe').and.returnValue({
						subscribe: mockSubscribeCallback,
					}),
				},
			};

			component.airPortInfoComponent.airportInfoForm.get = jasmine.createSpy('get').and.callFake((path: string) => {
				if (path === 'declaredValueForCarriage') {
					return {
						get: jasmine.createSpy('get').and.returnValue(mockDeclaredValueControl),
					};
				}
				if (path === 'wtOrVal') {
					return { value: 'COLLECT' };
				}
				return null;
			});

			component.ngAfterViewInit();

			// Simulate the subscription callback being called with a value
			const subscriptionCallback = mockSubscribeCallback.calls.mostRecent().args[0];
			subscriptionCallback('150.25');

			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				valuationChargeCollect: 150.25,
			});
		});

		it('should setup other charges payment type subscription', () => {
			const mockValueChanges = {
				pipe: jasmine.createSpy('pipe').and.returnValue({
					subscribe: jasmine.createSpy('subscribe'),
				}),
			};

			const mockOtherControl = {
				valueChanges: mockValueChanges,
			};

			component.airPortInfoComponent.airportInfoForm.get = jasmine.createSpy('get').and.callFake((path: string) => {
				if (path === 'other') {
					return mockOtherControl;
				}
				return null;
			});

			component.ngAfterViewInit();

			expect(mockValueChanges.pipe).toHaveBeenCalled();
		});

		it('should update other charges payment type to PREPAID when other value is PREPAID', () => {
			const mockSubscribeCallback = jasmine.createSpy('subscribe');
			const mockOtherControl = {
				valueChanges: {
					pipe: jasmine.createSpy('pipe').and.returnValue({
						subscribe: mockSubscribeCallback,
					}),
				},
			};

			component.airPortInfoComponent.airportInfoForm.get = jasmine.createSpy('get').and.callFake((path: string) => {
				if (path === 'other') {
					return mockOtherControl;
				}
				return null;
			});

			component.ngAfterViewInit();

			// Simulate the subscription callback being called with a value
			const subscriptionCallback = mockSubscribeCallback.calls.mostRecent().args[0];
			subscriptionCallback(DropDownType.PREPAID);

			expect(component.otherChargesComponent.otherChargesForm.patchValue).toHaveBeenCalledWith({
				chargePaymentType: 'Prepaid',
			});
		});

		it('should update other charges payment type to COLLECT when other value is not PREPAID', () => {
			const mockSubscribeCallback = jasmine.createSpy('subscribe');
			const mockOtherControl = {
				valueChanges: {
					pipe: jasmine.createSpy('pipe').and.returnValue({
						subscribe: mockSubscribeCallback,
					}),
				},
			};

			component.airPortInfoComponent.airportInfoForm.get = jasmine.createSpy('get').and.callFake((path: string) => {
				if (path === 'other') {
					return mockOtherControl;
				}
				return null;
			});

			component.ngAfterViewInit();

			// Simulate the subscription callback being called with a value
			const subscriptionCallback = mockSubscribeCallback.calls.mostRecent().args[0];
			subscriptionCallback(DropDownType.COLLECT);

			expect(component.otherChargesComponent.otherChargesForm.patchValue).toHaveBeenCalledWith({
				chargePaymentType: 'Collect',
			});
		});
	});

	describe('Constructor Navigation State Handling', () => {
		it('should extract hawbList from navigation state when available', () => {
			const mockNavigation = {
				extras: {
					state: {
						selectedHawbs: [
							{ hawbNumber: 'HAWB001', hawbId: 'hawb-1' },
							{ hawbNumber: 'HAWB002', hawbId: 'hawb-2' },
						],
					},
				},
			} as any;

			routerSpy.getCurrentNavigation.and.returnValue(mockNavigation);

			// Create new component instance to test constructor
			const newFixture = TestBed.createComponent(CreateMawbFromHawbComponent);
			const newComponent = newFixture.componentInstance;

			expect(newComponent.hawbList).toEqual(
				jasmine.arrayContaining([
					jasmine.objectContaining({ hawbNumber: 'HAWB001', hawbId: 'hawb-1' }),
					jasmine.objectContaining({ hawbNumber: 'HAWB002', hawbId: 'hawb-2' }),
				])
			);
			expect(newComponent.hawbNumberList).toEqual(['HAWB001', 'HAWB002']);
		});

		it('should handle empty navigation state gracefully', () => {
			routerSpy.getCurrentNavigation.and.returnValue(null);

			// Create new component instance to test constructor
			const newFixture = TestBed.createComponent(CreateMawbFromHawbComponent);
			const newComponent = newFixture.componentInstance;

			expect(newComponent.hawbList).toEqual([]);
			expect(newComponent.hawbNumberList).toEqual([]);
		});

		it('should handle navigation state without selectedHawbs', () => {
			const mockNavigation = {
				extras: {
					state: {
						otherData: 'some other data',
					},
				},
			} as any;

			routerSpy.getCurrentNavigation.and.returnValue(mockNavigation);

			// Create new component instance to test constructor
			const newFixture = TestBed.createComponent(CreateMawbFromHawbComponent);
			const newComponent = newFixture.componentInstance;

			expect(newComponent.hawbList).toEqual([]);
			expect(newComponent.hawbNumberList).toEqual([]);
		});
	});

	describe('Error Handling in onSave', () => {
		it('should handle update MAWB error gracefully', () => {
			component.mawbId = 'mawb-123';
			component['orgId'] = 'org-123';

			// Setup valid form data
			component.mawbForm.patchValue({
				mawbPrefix: 'AA',
				mawbNumber: '********',
				grossWeight: 100,
				chargeableWeight: 80,
				rateCharge: { currencyUnit: 'USD', numericalValue: 50 },
				natureAndQuantityOfGoods: 'Test goods',
				date: new Date(),
				atPlace: 'Test place',
				signatureOfShipperOrHisAgent: 'Test signature',
				signatureOfCarrierOrItsAgent: 'Carrier signature',
			});

			// Mock form validation to pass
			Object.defineProperty(component.mawbForm, 'invalid', { value: false, configurable: true });
			Object.defineProperty(component.airPortInfoComponent.airportInfoForm, 'invalid', { value: false, configurable: true });
			Object.defineProperty(component.carrierAgentComponent.carrierAgentForm, 'invalid', { value: false, configurable: true });

			// Mock getFormData methods to return valid data
			component.shipperOrConsigneeInfoComponentList.first.getFormData = jasmine.createSpy('getFormData').and.returnValue({
				companyType: 'SHIPPER',
				companyName: 'Test Shipper',
			});
			component.shipperOrConsigneeInfoComponentList.last.getFormData = jasmine.createSpy('getFormData').and.returnValue({
				companyType: 'CONSIGNEE',
				companyName: 'Test Consignee',
			});
			component.carrierAgentComponent.getFormData = jasmine.createSpy('getFormData').and.returnValue({
				companyType: 'CARRIER',
				companyName: 'Test Carrier',
			});
			component.issuedByComponent.getData = jasmine.createSpy('getData').and.returnValue({
				companyType: 'ISSUER',
				companyName: 'Test Issuer',
			});

			// Mock updateMawb to return error observable
			mawbCreateRequestServiceSpy.updateMawb.and.returnValue(
				of(null).pipe(
					tap(() => {
						throw new Error('Update failed');
					})
				)
			);

			component.onSave();

			expect(notificationServiceSpy.showError).toHaveBeenCalledWith('mawb.updateMawb.error');
		});
	});
});
