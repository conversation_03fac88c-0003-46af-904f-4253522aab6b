import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';

@Injectable({ providedIn: 'root' })
export class NotificationService {
	constructor(private readonly snackBar: MatSnackBar) {}

	showSuccess(message: string): void {
		this.snackBar.open(message, undefined, {
			panelClass: ['snackbar--success'],
			duration: 3000,
			verticalPosition: 'top',
		});
	}

	showError(message: string): void {
		this.snackBar.open(message, undefined, {
			panelClass: ['snackbar--error'],
			duration: 3000,
			verticalPosition: 'top',
		});
	}

	showProgress(message: string): void {
		this.snackBar.open(message, undefined, {
			duration: 0,
			verticalPosition: 'top',
		});
	}
}
