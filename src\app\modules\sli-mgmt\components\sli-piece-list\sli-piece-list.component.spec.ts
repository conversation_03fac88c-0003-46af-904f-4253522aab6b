import { ComponentFixture, TestBed, fakeAsync, tick, discardPeriodicTasks } from '@angular/core/testing';
import { SliPieceListComponent } from './sli-piece-list.component';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { of, throwError } from 'rxjs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { Sort } from '@angular/material/sort';
import { PageEvent } from '@angular/material/paginator';
import { PieceList } from '../../models/piece/piece-list.model';
import { ChangeDetectorRef } from '@angular/core';

describe('SliPieceListComponent', () => {
	let component: SliPieceListComponent;
	let fixture: ComponentFixture<SliPieceListComponent>;
	let mockSliCreateRequestService: jasmine.SpyObj<SliCreateRequestService>;
	let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;

	const mockPieceListResponse = {
		rows: [
			{
				type: 'Piece',
				pieceId: 'TEST123',
				productDescription: '123',
				packagingType: '456',
				grossWeight: 10,
				dimensions: {
					length: 10,
					width: 10,
					height: 10,
				},
				pieceQuantity: 1,
				slac: 0,
			},
			{
				type: 'Piece',
				pieceId: 'TEST456',
				productDescription: '456',
				packagingType: '789',
				grossWeight: 10,
				dimensions: {
					length: 10,
					width: 10,
					height: 10,
				},
				pieceQuantity: 1,
				slac: 0,
			},
		] as PieceList[],
		total: 2,
	};

	beforeEach(async () => {
		// Create spy objects with all required methods
		mockSliCreateRequestService = jasmine.createSpyObj<SliCreateRequestService>('SliCreateRequestService', [
			'getCurrencies',
			'getIncoterms',
			'getPieceList',
		]);
		mockChangeDetectorRef = jasmine.createSpyObj<ChangeDetectorRef>('ChangeDetectorRef', ['markForCheck']);

		// Configure mock return values
		mockSliCreateRequestService.getPieceList.and.returnValue(of(mockPieceListResponse));

		await TestBed.configureTestingModule({
			imports: [
				SliPieceListComponent,
				TranslateModule.forRoot(),
				ReactiveFormsModule,
				NoopAnimationsModule,
				MatFormFieldModule,
				MatInputModule,
				MatSelectModule,
			],
			providers: [
				{ provide: SliCreateRequestService, useValue: mockSliCreateRequestService },
				{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(SliPieceListComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	describe('Component Initialization', () => {
		it('should create the component', () => {
			expect(component).toBeTruthy();
		});

		it('should fetch piece list when sliNumber is provided', fakeAsync(() => {
			component.sliNumber = 'TEST-SLI-123';

			component.ngOnInit();
			tick(2000); // Wait for the setTimeout to complete

			expect(mockSliCreateRequestService.getPieceList).toHaveBeenCalledWith(component.pageParams, 'TEST-SLI-123');
			expect(component.pieceList).toEqual(mockPieceListResponse.rows);
			expect(component.totalRecords).toBe(mockPieceListResponse.total);

			discardPeriodicTasks(); // Clean up any pending timers
		}));

		it('should not fetch piece list when sliNumber is not provided', fakeAsync(() => {
			component.sliNumber = '';

			component.ngOnInit();
			tick(2000); // Wait for the setTimeout to complete

			expect(mockSliCreateRequestService.getPieceList).not.toHaveBeenCalled();

			discardPeriodicTasks(); // Clean up any pending timers
		}));
	});

	describe('Sorting and Pagination', () => {
		it('should update pageParams when onSortChange is called with direction', () => {
			const sort: Sort = { active: 'productDescription', direction: 'asc' };

			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('productDescription');
			expect(component.pageParams.isAsc).toBe('asc');
		});

		it('should clear sort parameters when onSortChange is called with empty direction', () => {
			// First set some values
			component.pageParams.orderByColumn = 'productDescription';
			component.pageParams.isAsc = 'asc';

			// Then clear them
			const sort: Sort = { active: 'productDescription', direction: '' };
			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('');
			expect(component.pageParams.isAsc).toBe('');
		});

		it('should update pageParams and fetch data when onPageChange is called', () => {
			spyOn<any>(component, 'getPieceListPage').and.callThrough();

			const pageEvent: PageEvent & { sortField?: string; sortDirection?: string } = {
				pageIndex: 2,
				pageSize: 50,
				length: 100,
				sortField: 'grossWeight',
				sortDirection: 'desc',
			};

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(3); // pageIndex + 1
			expect(component.pageParams.pageSize).toBe(50);
			expect(component.pageParams.orderByColumn).toBe('grossWeight');
			expect(component.pageParams.isAsc).toBe('desc');
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should update pageParams without sort parameters when onPageChange is called without sort data', () => {
			spyOn<any>(component, 'getPieceListPage').and.callThrough();

			const pageEvent: PageEvent = {
				pageIndex: 2,
				pageSize: 50,
				length: 100,
			};

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(3); // pageIndex + 1
			expect(component.pageParams.pageSize).toBe(50);
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		});
	});

	describe('getPieceListPage Method', () => {
		it('should fetch piece list data and update component properties', fakeAsync(() => {
			component.sliNumber = 'TEST-SLI-123';
			component.dataLoading = false;
			component.pieceList = [];
			component.totalRecords = 0;

			component['getPieceListPage'](component.pageParams);
			tick();

			expect(mockSliCreateRequestService.getPieceList).toHaveBeenCalledWith(component.pageParams, 'TEST-SLI-123');
			expect(component.pieceList).toEqual(mockPieceListResponse.rows);
			expect(component.totalRecords).toBe(mockPieceListResponse.total);
			expect(component.dataLoading).toBe(false);
		}));

		it('should handle error when fetching piece list data', fakeAsync(() => {
			mockSliCreateRequestService.getPieceList.and.returnValue(throwError(() => new Error('Test error')));

			component.sliNumber = 'TEST-SLI-123';
			component.dataLoading = false;

			component['getPieceListPage'](component.pageParams);
			tick();

			expect(component.dataLoading).toBe(false);
		}));
	});
});
