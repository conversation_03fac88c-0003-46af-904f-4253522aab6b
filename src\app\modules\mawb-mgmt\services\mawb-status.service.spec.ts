import { TestBed } from '@angular/core/testing';
import { MawbStatusService } from './mawb-status.service';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting, HttpTestingController } from '@angular/common/http/testing';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { ResponseObj } from '@shared/models/response.model';
import { MAWBUpdateEventObj } from '../models/mawb-event.model';
import { environment } from '@environments/environment';

const baseUrl = environment.baseApi;

describe('MawbStatusService', () => {
	let service: MawbStatusService;
	let httpMock: HttpTestingController;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [
				MawbStatusService,
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		});
		service = TestBed.inject(MawbStatusService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify();
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('should send a POST request to update status', () => {
		const mockParam: MAWBUpdateEventObj = {
			status: 'MOP',
			updateTime: '',
			eventTimeType: '',
			mawbId: '',
			choseAllHAWB: false,
			hawbIdList: [],
		};

		const mockResponse: ResponseObj<string> = {
			code: 200,
			data: 'Status updated',
			msg: '',
		};

		service.updateEventStatus(mockParam).subscribe((response) => {
			expect(response).toEqual('Status updated');
		});

		const req = httpMock.expectOne(`${baseUrl}/event-management/update-choose-status`);
		expect(req.request.method).toBe('POST');
		expect(req.request.body).toEqual(mockParam);
		req.flush(mockResponse);
	});
});
