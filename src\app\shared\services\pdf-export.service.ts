import { Injectable } from '@angular/core';

@Injectable({
	providedIn: 'root',
})
export class PdfExportService {
	/**
	 * Export HTML element to PDF
	 * @param element - The HTML element to export
	 * @param filename - The name of the PDF file (without extension)
	 * @param options - Additional options for PDF generation
	 */
	async exportToPdf(
		element: HTMLElement,
		filename = 'document',
		options: {
			format?: 'a4' | 'a3' | 'letter';
			orientation?: 'portrait' | 'landscape';
			quality?: number;
			scale?: number;
			margin?: number;
		} = {}
	): Promise<void> {
		try {
			// Dynamic imports to avoid build issues
			const [jsPDFModule, html2canvasModule] = await Promise.all([
				import('jspdf').then((m: any) => m.default || m),
				import('html2canvas').then((m: any) => m.default || m),
			]);
			const jsPDF = jsPDFModule;
			const html2canvas = html2canvasModule;

			// Default options
			const defaultOptions = {
				format: 'a4' as const,
				orientation: 'portrait' as const,
				quality: 1,
				scale: 2,
				margin: 10,
			};

			const config = { ...defaultOptions, ...options };

			// Configure html2canvas options
			const canvasOptions = {
				scale: config.scale,
				useCORS: true,
				allowTaint: true,
				backgroundColor: '#ffffff',
				logging: false,
				width: element.scrollWidth,
				height: element.scrollHeight,
				scrollX: 0,
				scrollY: 0,
			};

			// Convert HTML to canvas
			const canvas = await html2canvas(element, canvasOptions);
			const imgData = canvas.toDataURL('image/png', config.quality);

			// Calculate PDF dimensions
			const imgWidth = canvas.width;
			const imgHeight = canvas.height;

			// Create PDF with specified format and orientation
			const pdf = new jsPDF({
				orientation: config.orientation,
				unit: 'mm',
				format: config.format,
			});

			// Get PDF page dimensions
			const pdfWidth = pdf.internal.pageSize.getWidth();
			const pdfHeight = pdf.internal.pageSize.getHeight();

			// Calculate scaling to fit content within margins
			const availableWidth = pdfWidth - config.margin * 2;
			const availableHeight = pdfHeight - config.margin * 2;

			// Calculate aspect ratios
			const imgAspectRatio = imgWidth / imgHeight;
			const pageAspectRatio = availableWidth / availableHeight;

			let finalWidth: number;
			let finalHeight: number;

			// Scale to fit within available space while maintaining aspect ratio
			if (imgAspectRatio > pageAspectRatio) {
				// Image is wider relative to page
				finalWidth = availableWidth;
				finalHeight = availableWidth / imgAspectRatio;
			} else {
				// Image is taller relative to page
				finalHeight = availableHeight;
				finalWidth = availableHeight * imgAspectRatio;
			}

			// Center the image on the page
			const xOffset = (pdfWidth - finalWidth) / 2;
			const yOffset = (pdfHeight - finalHeight) / 2;

			// Add image to PDF
			pdf.addImage(imgData, 'PNG', xOffset, yOffset, finalWidth, finalHeight);

			// Save the PDF
			pdf.save(`${filename}.pdf`);
		} catch (error) {
			console.error('Error generating PDF:', error);
			throw new Error('Failed to generate PDF. Please try again.');
		}
	}

	/**
	 * Export multiple HTML elements to a single PDF with multiple pages
	 * @param elements - Array of HTML elements to export
	 * @param filename - The name of the PDF file (without extension)
	 * @param options - Additional options for PDF generation
	 */
	async exportMultiPageToPdf(
		elements: HTMLElement[],
		filename = 'document',
		options: {
			format?: 'a4' | 'a3' | 'letter';
			orientation?: 'portrait' | 'landscape';
			quality?: number;
			scale?: number;
			margin?: number;
		} = {}
	): Promise<void> {
		try {
			// Dynamic imports to avoid build issues
			const [jsPDFModule, html2canvasModule] = await Promise.all([
				import('jspdf').then((m: any) => m.default || m),
				import('html2canvas').then((m: any) => m.default || m),
			]);
			const jsPDF = jsPDFModule;
			const html2canvas = html2canvasModule;

			// Default options
			const defaultOptions = {
				format: 'a4' as const,
				orientation: 'portrait' as const,
				quality: 1,
				scale: 2,
				margin: 10,
			};

			const config = { ...defaultOptions, ...options };

			// Create PDF with specified format and orientation
			const pdf = new jsPDF({
				orientation: config.orientation,
				unit: 'mm',
				format: config.format,
			});

			// Get PDF page dimensions
			const pdfWidth = pdf.internal.pageSize.getWidth();
			const pdfHeight = pdf.internal.pageSize.getHeight();
			const availableWidth = pdfWidth - config.margin * 2;
			const availableHeight = pdfHeight - config.margin * 2;

			for (let i = 0; i < elements.length; i++) {
				const element = elements[i];

				// Configure html2canvas options
				const canvasOptions = {
					scale: config.scale,
					useCORS: true,
					allowTaint: true,
					backgroundColor: '#ffffff',
					logging: false,
					width: element.scrollWidth,
					height: element.scrollHeight,
					scrollX: 0,
					scrollY: 0,
				};

				// Convert HTML to canvas
				const canvas = await html2canvas(element, canvasOptions);
				const imgData = canvas.toDataURL('image/png', config.quality);

				// Calculate dimensions
				const imgWidth = canvas.width;
				const imgHeight = canvas.height;
				const imgAspectRatio = imgWidth / imgHeight;
				const pageAspectRatio = availableWidth / availableHeight;

				let finalWidth: number;
				let finalHeight: number;

				// Scale to fit within available space while maintaining aspect ratio
				if (imgAspectRatio > pageAspectRatio) {
					finalWidth = availableWidth;
					finalHeight = availableWidth / imgAspectRatio;
				} else {
					finalHeight = availableHeight;
					finalWidth = availableHeight * imgAspectRatio;
				}

				// Center the image on the page
				const xOffset = (pdfWidth - finalWidth) / 2;
				const yOffset = (pdfHeight - finalHeight) / 2;

				// Add new page if not the first element
				if (i > 0) {
					pdf.addPage();
				}

				// Add image to PDF
				pdf.addImage(imgData, 'PNG', xOffset, yOffset, finalWidth, finalHeight);
			}

			// Save the PDF
			pdf.save(`${filename}.pdf`);
		} catch (error) {
			console.error('Error generating multi-page PDF:', error);
			throw new Error('Failed to generate PDF. Please try again.');
		}
	}

	/**
	 * Get optimal PDF options for AWB documents
	 */
	getAwbPdfOptions() {
		return {
			format: 'a4' as const,
			orientation: 'landscape' as const,
			quality: 0.95,
			scale: 2,
			margin: 5,
		};
	}
}
