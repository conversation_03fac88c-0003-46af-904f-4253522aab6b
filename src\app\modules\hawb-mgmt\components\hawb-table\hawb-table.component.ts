import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { MatPaginatorIntl, MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { CustomPaginatorIntl } from '@shared/services/custom-paginator-intl.service';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { HawbListObject } from '../../models/hawb-list-object.model';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatSortModule, Sort } from '@angular/material/sort';
import { Router } from '@angular/router';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { Modules, UserPermission } from '@shared/models/user-role.model';
import { AsyncPipe } from '@angular/common';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SelectionModel } from '@angular/cdk/collections';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { forkJoin } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';

@Component({
	selector: 'orll-hawb-table',
	templateUrl: './hawb-table.component.html',
	styleUrls: ['./hawb-table.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatTableModule,
		MatSortModule,
		MatButtonModule,
		MatCheckboxModule,
		MatMenuModule,
		MatIconModule,
		MatDividerModule,
		MatPaginatorModule,
		TranslateModule,
		AsyncPipe,
	],
	providers: [{ provide: MatPaginatorIntl, useClass: CustomPaginatorIntl }],
})
export class HawbTableComponent extends RolesAwareComponent implements OnChanges, OnInit {
	@Input() records: HawbListObject[] = [];
	@Input() totalRecords = 0;
	@Input() pageParams!: PaginationRequest;
	@Input() fromCreateMawb = false;
	@Input() isFHL = false;

	@Output() shareHawb: EventEmitter<HawbListObject> = new EventEmitter<HawbListObject>();
	@Output() sortChange = new EventEmitter<Sort>();
	@Output() pagination = new EventEmitter<PageEvent & { sortField?: string; sortDirection?: string }>();

	currentSort: Sort = { active: '', direction: '' };

	displayedColumns: string[] = ['hawbNumber', 'shipper', 'consignee', 'goodsDescription', 'origin', 'destination', 'createDate'];
	readonly tablePageSizes: number[] = [10, 50, 100];

	dataSource = new MatTableDataSource<HawbListObject>(this.records || []);
	selection = new SelectionModel<HawbListObject>(true, []);
	totalSlac = 0;
	totalPiece = 0;

	readonly hawbModule = Modules.HAWB;
	readonly mawbModule = Modules.MAWB;
	readonly createPermission = UserPermission.CREATE;
	readonly sharePermission = UserPermission.SHARE;

	constructor(private readonly router: Router) {
		super();
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['records']) {
			this.dataSource.data = this.records;
			this.totalSlac = this.records.reduce((total, current) => total + (current.slac ?? 0), 0);
			this.totalPiece = this.records.reduce((total, current) => total + Number(current.pieceQuantity), 0);
			this.selection.clear();
		}
	}

	ngOnInit(): void {
		const roleChecks$ = forkJoin({
			hasCreatePermission: this.hasPermission(this.createPermission, this.mawbModule),
			hasSharePermission: this.hasPermission(this.sharePermission, this.hawbModule),
		});

		roleChecks$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(({ hasCreatePermission, hasSharePermission }) => {
			const newColumns = [...this.displayedColumns];

			if (hasCreatePermission && this.fromCreateMawb && !this.isFHL) {
				newColumns.unshift('select');
			}

			if (hasSharePermission && !this.fromCreateMawb) {
				newColumns.push('share');
			}

			if (!this.fromCreateMawb) {
				newColumns.push('mawbNumber');
			} else if (this.isFHL) {
				newColumns.pop(); // pop createDate
				newColumns.push('pieceQuantity');
				newColumns.push('totalGrossWeight');
				newColumns.push('slac');
				newColumns.push('countryCode');
				newColumns.push('contentCode');
				newColumns.push('otherCustomsInformation');
			}

			this.displayedColumns = newColumns;
		});
	}

	onSortChange(event: Sort): void {
		this.currentSort = event;
		this.sortChange.emit(event);
		this.emitPaginationWithSort();
	}

	private emitPaginationWithSort(event?: PageEvent) {
		const pageEvent = event || {
			pageIndex: this.pageParams.pageNum - 1,
			pageSize: this.pageParams.pageSize,
			length: this.totalRecords,
		};

		this.pagination.emit({
			...pageEvent,
			sortField: this.currentSort.active,
			sortDirection: this.currentSort.direction,
		});
	}

	isAllSelected() {
		const numSelected = this.selection.selected.length;
		const numRows = this.dataSource.data.length;
		return numRows > 0 ? numSelected === numRows : false;
	}

	toggleAllRows() {
		if (this.isAllSelected()) {
			this.selection.clear();
			return;
		}
		this.selection.select(...this.dataSource.data);
	}

	createHawbFromSli(): void {
		this.router.navigate(['/hawb/create']);
	}

	createMawbFromHawb(): void {
		const selectedHawbs = this.selection.selected;
		this.router.navigate(['/mawb/create/detail'], {
			state: {
				selectedHawbs: selectedHawbs,
			},
		});
	}

	editHawb(hawbId: string): void {
		this.router.navigate(['/hawb/edit', hawbId]);
	}

	trackByHawbId(record: HawbListObject): string {
		return record.hawbId + record.createDate;
	}
}
