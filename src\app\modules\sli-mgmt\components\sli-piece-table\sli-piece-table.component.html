<div class="orll-sli-piece-table__container">
	<div class="orll-sli-piece-table__create">
		<button mat-stroked-button color="primary" [disabled]="dataSource.data.length === 0 || !selection.hasValue()"
		(click)="delPiece($event, selection?.selected)" class="orll-sli-piece-table__delete-button">
			<mat-icon>delete</mat-icon>
			{{'sli.mgmt.delete.piece' | translate}}
		</button>
		<button mat-flat-button color="primary" (click)="addPiece()" class="orll-sli-piece-table__add-button">
			<mat-icon>add</mat-icon>
			{{'sli.mgmt.add.piece' | translate}}
		</button>
	</div>
	<table mat-table [dataSource]="dataSource" [trackBy]="trackByPieceId"
		   matSort
		   (matSortChange)="onSortChange($event)"
		   aria-label="SLI Piece table"
		   class="orll-sli-piece-table">

		<ng-container matColumnDef="select">
			<th mat-header-cell *matHeaderCellDef>
				<mat-checkbox
					(change)="toggleAllRows()"
					[checked]="isAllSelected()"
					[disabled]="dataSource.data.length === 0"
					[indeterminate]="selection.hasValue() && !isAllSelected()">
				</mat-checkbox>
			</th>
			<td mat-cell *matCellDef="let row">
				<mat-checkbox
					(click)="$event.stopPropagation()"
					(keydown.enter)="$event.stopPropagation()"
					(change)="selection.toggle(row)"
					[checked]="selection.isSelected(row)">
				</mat-checkbox>
			</td>
		</ng-container>

		<ng-container matColumnDef="productDescription">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>{{'sli.piece.table.column.productDescription' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.productDescription}}</td>
		</ng-container>

		<ng-container matColumnDef="packagingType">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>{{'sli.piece.table.column.packagingType' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.packagingType}}</td>
		</ng-container>

		<ng-container matColumnDef="grossWeight">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>{{'sli.piece.table.column.grossWeight' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.grossWeight}}</td>
		</ng-container>

		<ng-container matColumnDef="dimensions">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>{{'sli.piece.table.column.dimensions' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.dimensions?.length}}CMx{{record.dimensions?.width}}CMx{{record.dimensions?.height}}CM</td>
		</ng-container>

		<ng-container matColumnDef="pieceQuantity">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>{{'sli.piece.table.column.pieceQuantity' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.pieceQuantity}}</td>
		</ng-container>

		<ng-container matColumnDef="slac">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>{{'sli.piece.table.column.slac' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.slac}}</td>
		</ng-container>

		<ng-container matColumnDef="actions">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>{{'sli.piece.table.column.actions' | translate}}</th>
			<td mat-cell *matCellDef="let record">
				<button mat-icon-button color="primary" (click)="editPiece($event, record)"
					class="orll-sli-piece-table__edit-button">
					<mat-icon>edit</mat-icon>
				</button>
				<button mat-icon-button color="primary"  (click)="delPiece($event, [record])"
					class="orll-sli-piece-table__row-delete-button">
					<mat-icon>delete</mat-icon>
				</button>
			</td>
		</ng-container>

		<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
		<tr mat-row *matRowDef="let record; columns: displayedColumns;" class="orll-sli-piece-table__row"></tr>
	</table>
</div>
<mat-paginator
   [pageSizeOptions]="tablePageSizes"
   [length]="totalRecords"
   (page)="pagination.emit($event)"
></mat-paginator>
@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
