import { ComponentFixture, TestBed } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import PartnerAccessComponent from './partner-access.component';
import { PartnerAccessRequestService } from '../../services/partner-access-request.service';
import { TranslateModule } from '@ngx-translate/core';
import { of } from 'rxjs';

describe('PartnerAccessComponent', () => {
	let component: PartnerAccessComponent;
	let fixture: ComponentFixture<PartnerAccessComponent>;
	const mockPartnerService: Partial<PartnerAccessRequestService> = {
		getPartnerList: jasmine.createSpy('getPartnerList').and.returnValue(
			of([
				{
					id: '111',
					businessData: 'sli',
					orgId: '1',
					orgName: '1',
					getLogisticsObject: '1',
					patchLogisticsObject: '1',
					postLogisticsEvent: '1',
					getLogisticsEvent: '1',
					createTime: '',
				},
				{
					id: '222',
					businessData: 'hawb',
					orgId: '2',
					orgName: '2',
					getLogisticsObject: '1',
					patchLogisticsObject: '1',
					postLogisticsEvent: '1',
					getLogisticsEvent: '1',
					createTime: '',
				},
			])
		),

		getOrgList: jasmine.createSpy('getOrgList').and.returnValue(
			of([
				{ id: '1', name: 'Org 1', orgType: 'TYPE_A' },
				{ id: '2', name: 'Org 2', orgType: 'TYPE_B' },
			])
		),

		getSystemList: jasmine.createSpy('getSystemList').and.returnValue(
			of([
				{ code: '1', name: 'sli' },
				{ code: '2', name: 'hawb' },
			])
		),

		addPartner: jasmine.createSpy('addPartner').and.returnValue(of({ code: 'Success', data: 'Success' })),
	};

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [PartnerAccessComponent, TranslateModule.forRoot()],
			providers: [{ provide: PartnerAccessRequestService, useValue: mockPartnerService }],
		}).compileComponents();

		fixture = TestBed.createComponent(PartnerAccessComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('addOrSaveRow() should make shouldShowEditUI is true', () => {
		expect(component.newRow).toBeNull();
		component.addOrSaveRow();

		expect(component.newRow).not.toBeNull();

		if (component.newRow) {
			expect(component.shouldShowEditUI(component.newRow)).toBeTrue();
		}

		component.addOrSaveRow();
		expect(component.newRow).toBeNull();
	});
});
