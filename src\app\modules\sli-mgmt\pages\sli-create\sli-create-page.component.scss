.orll-sli-create-page {
	margin-top: 40px;

	.iata-box {
		overflow-y: auto;
	}

	.tab-content-container {
		padding: 20px;
	}

	.row {
		gap: 20px;
		display: flex;
		margin-bottom: 10px;
		justify-content: space-between;
		.input-container {
			width: 16%;
		}
		.input-box {
			width: 100%;
			.autocomplete-arrow  {
			  width:100px;
			}
		.unit-row ,.all-inputs-row {
			border-radius: 5px 0px 0px 5px !important;
			display: flex;
			height: 33px;
			input {
				border:none;
			}
			:focus-visible {
  				outline: none !important;
 				box-shadow: none !important;
			}
			mat-icon {
				margin-top:5px;
			}
		}
		.unit-row span ,.all-inputs-row .divider{

				display: inline-block;
				margin-top:5px;
				width: 1px;
				height:31px;
				background-color: #ccc;
				margin-right:10px;
			}
		.all-inputs-row {
			padding-right:10px;
			.divider {
				margin-left: 10px;
			}
			span {
				display: inline-block;
				height: 33px;
				line-height:33px;
			}
		}
		.weight-unit {
			margin-right:10px;
		}
	}
		.iata-shipper-box {
			position: relative;
			margin-bottom: 20px;
			border-radius: 8px;
			padding: 20px;
			flex: 1;
			border: 1px solid var(--iata-grey-200);
		}

		.width-100 {
			width: 100%;
		}
		.width-10 {
			width: 10%;
		}
		.width-30 {
			width: 30%;
		}


		.width-13 {
			width: 13%;
		}

		.width-5 {
			width: 5%;
		}
		.fill {
			flex: 1 1 auto;
		}
	}
	.orll-sli-create-page__footer {
		display: flex;
		justify-content: flex-end;
  		gap: 10px;
	}
}
