import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MawbStatusListComponent } from './mawb-status-list.component';
import { of } from 'rxjs';
import { MawbStatusService } from '../../services/mawb-status.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { HTTP_INTERCEPTORS, provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { TranslateModule } from '@ngx-translate/core';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { UserProfile } from '@shared/models/user-profile.model';

function createMockUserProfile(overrides: Partial<UserProfile> = {}): UserProfile {
	return {
		userId: 'test-user-id',
		email: '<EMAIL>',
		firstName: 'Test',
		lastName: 'User',
		primaryOrgId: 'primary-org',
		primaryOrgName: 'Primary Org',
		orgId: 'org-id',
		orgName: 'Org Name',
		orgType: 'ORG_TYPE',
		userType: '1',
		menuList: [],
		permissionList: [],
		orgList: [],
		...overrides,
	};
}

describe('MawbStatusListComponent', () => {
	let component: MawbStatusListComponent;
	let fixture: ComponentFixture<MawbStatusListComponent>;
	let statusService: jasmine.SpyObj<MawbStatusService>;

	const mockStatusService = {
		getMawbStatus: jasmine.createSpy('getMawbStatus').and.returnValue(
			of({
				mawbId: '',
				latestStatus: '',
				updateTime: '',
				orgName: '',
				userName: '',
				updateBy: '',
				checked: false,
				opened: false,
			})
		),
		listHawbStatusByMawb: jasmine.createSpy('listHawbStatusByMawb').and.returnValue(of({ rows: [{ hawbId: '1' }] })),
		listPieceStatusByHawb: jasmine.createSpy('listPieceStatusByHawb').and.returnValue(of({ rows: [{ id: '1' }] })),
	};

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [MawbStatusListComponent, RolesAwareComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: MawbStatusService, useValue: mockStatusService },
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClientTesting(),
				provideHttpClient(),
				{ provide: takeUntilDestroyed, useValue: () => (source: any) => source },
			],
		}).compileComponents();
		fixture = TestBed.createComponent(MawbStatusListComponent);
		component = fixture.componentInstance;
		const mockUser = createMockUserProfile({ primaryOrgId: 'org123' });
		spyOn(component, 'getCurrentUser').and.returnValue(of(mockUser));
		component.mawbStatus = {
			mawbId: '',
			latestStatus: '',
			updateTime: '',
			orgName: '',
			userName: '',
			updateBy: '',
			checked: false,
			opened: false,
			code: '',
		};
		fixture.detectChanges();
		statusService = TestBed.inject(MawbStatusService) as jasmine.SpyObj<MawbStatusService>;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should not load children if already loaded', () => {
		const mockEvent: any = {
			opened: false,
			pieceStatusList: ['some-data'],
			hawbId: 'parent2',
		};

		component.togglePanel(mockEvent);

		expect(mockEvent.opened).toBe(true);
	});

	it('should load children and update dataLoading and childrenList', () => {
		const mockParent: any = {
			hawbId: 'parent3',
			pageNum: 1,
			pieceStatusList: [],
			opened: false,
			checked: false,
		};
		const mockRes: any = {
			pageNum: 1,
			total: 6,
			rows: [
				{
					pieceId: '1111',
					updateTime: '2025-01-01 12:00:00Z',
					latestStatus: 'UP',
					orgName: '1111',
					userName: 'tom',
					updateBy: '111:tom',
					checked: false,
					opened: false,
				},
				{
					pieceId: '2222',
					updateTime: '2025-01-01 12:00:00Z',
					latestStatus: 'UP',
					orgName: '1111',
					userName: 'tom',
					updateBy: '111:tom',
					checked: false,
					opened: false,
				},
				{
					pieceId: '33333',
					updateTime: '2025-01-01 12:00:00Z',
					latestStatus: 'UP',
					orgName: '1111',
					userName: 'tom',
					updateBy: '111:tom',
					checked: false,
					opened: false,
				},
			],
		};

		statusService.listPieceStatusByHawb.and.returnValue(of(mockRes));

		component.loadPieces(mockParent);

		expect(component.dataLoading).toBe(false);
	});

	describe('toggleParent', () => {
		it('should toggle parent and check all children', () => {
			component.toggleMawb();

			expect(component.mawbStatus?.checked).toBe(true);
			component.hawbStatusList.forEach((child) => {
				expect(child.checked).toBe(true);
			});
		});

		it('should uncheck parent and uncheck all children', () => {
			component.mawbStatus!.checked = true;
			component.toggleMawb();

			expect(component.mawbStatus?.checked).toBe(false);
			component.hawbStatusList.forEach((child) => {
				expect(child.checked).toBe(false);
			});
		});
	});
});
