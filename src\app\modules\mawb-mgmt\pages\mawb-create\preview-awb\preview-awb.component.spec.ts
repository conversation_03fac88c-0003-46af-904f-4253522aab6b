import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { provideTranslateService, TranslateService } from '@ngx-translate/core';
import { DatePipe } from '@angular/common';
import { ChangeDetectorRef } from '@angular/core';

import { PreviewAwbComponent } from './preview-awb.component';
import { PdfExportService } from '@shared/services/pdf-export.service';
import { NotificationService } from '@shared/services/notification.service';
import { OrgType } from '@shared/models/org-type.model';
import { AccountingNoteIdentifier } from '../../../models/accounting-note-identifier.model';
import { MawbCreateDto, PartyList, AccountingNote } from '../../../models/mawb-create.model';
import { AirportInfoComponent } from '../airport-info/airport-info.component';

describe('PreviewAwbComponent', () => {
	let component: PreviewAwbComponent;
	let fixture: ComponentFixture<PreviewAwbComponent>;
	let mockDialogRef: jasmine.SpyObj<MatDialogRef<PreviewAwbComponent>>;
	let mockPdfExportService: jasmine.SpyObj<PdfExportService>;
	let mockNotificationService: jasmine.SpyObj<NotificationService>;
	let mockTranslateService: jasmine.SpyObj<TranslateService>;
	let mockDatePipe: jasmine.SpyObj<DatePipe>;
	let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;
	let mockAirportInfoComponent: jasmine.SpyObj<AirportInfoComponent>;
	let mockMawbData: MawbCreateDto & {
		total: number | null;
		destinationCollectCharges: number | null;
		totalCollectCharges: number | null;
		prepaidFormData: any;
		airPortInfoComponent: AirportInfoComponent;
	};

	beforeEach(async () => {
		mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
		mockPdfExportService = jasmine.createSpyObj('PdfExportService', ['exportToPdf', 'getAwbPdfOptions']);
		mockNotificationService = jasmine.createSpyObj('NotificationService', ['showProgress', 'showSuccess', 'showError']);
		mockTranslateService = jasmine.createSpyObj('TranslateService', ['instant']);
		mockDatePipe = jasmine.createSpyObj('DatePipe', ['transform']);
		mockChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);
		mockAirportInfoComponent = jasmine.createSpyObj('AirportInfoComponent', ['displayAirportName']);

		// Setup default return values
		mockPdfExportService.getAwbPdfOptions.and.returnValue({
			format: 'a4' as const,
			orientation: 'landscape' as const,
			quality: 0.95,
			scale: 2,
			margin: 5,
		});
		mockTranslateService.instant.and.returnValue('Translated text');
		mockDatePipe.transform.and.returnValue('2024-01-01');
		mockAirportInfoComponent.displayAirportName.and.returnValue('Airport Name');

		// Create mock data
		const mockPartyList: PartyList[] = [
			{
				companyName: 'Shipper Company',
				contactName: 'John Doe',
				countryCode: 'US',
				regionCode: 'NY',
				locationName: 'New York',
				cityCode: 'NYC',
				textualPostCode: '10001',
				phoneNumber: '+********90',
				emailAddress: '<EMAIL>',
				companyType: OrgType.SHIPPER,
			},
			{
				companyName: 'Consignee Company',
				contactName: 'Jane Smith',
				countryCode: 'UK',
				regionCode: 'LDN',
				locationName: 'London',
				cityCode: 'LHR',
				textualPostCode: 'SW1A 1AA',
				phoneNumber: '+44********9',
				emailAddress: '<EMAIL>',
				companyType: OrgType.CONSIGNEE,
			},
			{
				companyName: 'Forwarder Company',
				contactName: 'Bob Johnson',
				countryCode: 'DE',
				regionCode: 'BER',
				locationName: 'Berlin',
				cityCode: 'BER',
				textualPostCode: '10115',
				phoneNumber: '+49********9',
				emailAddress: '<EMAIL>',
				companyType: OrgType.FORWARDER,
			},
			{
				companyName: 'Carrier Company',
				contactName: 'Alice Brown',
				countryCode: 'FR',
				regionCode: 'PAR',
				locationName: 'Paris',
				cityCode: 'CDG',
				textualPostCode: '75001',
				phoneNumber: '+33********9',
				emailAddress: '<EMAIL>',
				companyType: OrgType.CARRIER,
			},
		];

		const mockAccountingNoteList: AccountingNote[] = [
			{
				accountingNoteIdentifier: AccountingNoteIdentifier.SHIPPER,
				accountingNoteText: 'Shipper accounting note',
			},
			{
				accountingNoteIdentifier: AccountingNoteIdentifier.CONSIGNEE,
				accountingNoteText: 'Consignee accounting note',
			},
			{
				accountingNoteIdentifier: AccountingNoteIdentifier.FORWARDER,
				accountingNoteText: 'Forwarder accounting note',
			},
			{
				accountingNoteIdentifier: AccountingNoteIdentifier.CARRIER,
				accountingNoteText: 'Carrier accounting note',
			},
		];

		mockMawbData = {
			waybillPrefix: '123',
			waybillNumber: '********',
			houseWaybills: ['HWB001'],
			partyList: mockPartyList,
			accountingNoteList: mockAccountingNoteList,
			departureLocation: 'JFK',
			arrivalLocation: 'LHR',
			requestedFlight: 'AA123',
			requestedDate: '2024-01-01',
			toFirst: 'First destination',
			toSecond: 'Second destination',
			toThird: 'Third destination',
			byFirstCarrier: 'AA',
			bySecondCarrier: 'BA',
			byThirdCarrier: 'AF',
			insuredAmount: { currencyUnit: 'USD', numericalValue: 1000 },
			carrierChargeCode: 'CC',
			weightValuationIndicator: 'Y',
			otherChargesIndicator: 'N',
			declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: 500 },
			declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: 750 },
			textualHandlingInstructions: 'Handle with care',
			totalGrossWeight: 100,
			serviceCode: 'GEN',
			rateClassCode: 'N',
			totalVolumetricWeight: 50,
			rateCharge: { currencyUnit: 'USD', numericalValue: 200 },
			goodsDescription: 'General cargo',
			otherChargeList: [],
			destinationCurrencyRate: 1.2,
			destinationCharges: { currencyUnit: 'EUR', numericalValue: 150 },
			shippingInfo: 'Shipping information',
			shippingRefNo: 'REF123',
			carrierDeclarationDate: '2024-01-01',
			carrierDeclarationPlace: 'New York',
			consignorDeclarationSignature: 'John Doe',
			carrierDeclarationSignature: 'Carrier Rep',
			total: 1000,
			destinationCollectCharges: 200,
			totalCollectCharges: 300,
			prepaidFormData: {},
			airPortInfoComponent: mockAirportInfoComponent,
		};

		await TestBed.configureTestingModule({
			imports: [PreviewAwbComponent],
			providers: [
				{ provide: MatDialogRef, useValue: mockDialogRef },
				{ provide: MAT_DIALOG_DATA, useValue: mockMawbData },
				{ provide: PdfExportService, useValue: mockPdfExportService },
				{ provide: NotificationService, useValue: mockNotificationService },
				{ provide: TranslateService, useValue: mockTranslateService },
				{ provide: DatePipe, useValue: mockDatePipe },
				{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
				provideTranslateService(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(PreviewAwbComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should initialize component properties when data is provided', () => {
			expect(component.shipperInfo).toEqual(
				jasmine.objectContaining({
					companyName: 'Shipper Company',
					companyType: OrgType.SHIPPER,
				})
			);
			expect(component.consigneeInfo).toEqual(
				jasmine.objectContaining({
					companyName: 'Consignee Company',
					companyType: OrgType.CONSIGNEE,
				})
			);
			expect(component.carrierInfo).toEqual(
				jasmine.objectContaining({
					companyName: 'Forwarder Company',
					companyType: OrgType.FORWARDER,
				})
			);
			expect(component.issuedByInfo).toEqual(
				jasmine.objectContaining({
					companyName: 'Carrier Company',
					companyType: OrgType.CARRIER,
				})
			);
		});

		it('should set accounting notes correctly', () => {
			expect(component.shipperAccountingNote).toBe('Shipper accounting note');
			expect(component.consigneeAccountingNote).toBe('Consignee accounting note');
			expect(component.carrierAccountingNote).toBe('Forwarder accounting note');
			expect(component.issuedByAccountingNote).toBe('Carrier accounting note');
		});

		it('should set airport names using displayAirportName method', () => {
			expect(mockAirportInfoComponent.displayAirportName).toHaveBeenCalledWith('JFK');
			expect(mockAirportInfoComponent.displayAirportName).toHaveBeenCalledWith('LHR');
			expect(component.airportOfDeparture).toBe('Airport Name');
			expect(component.airportOfDestination).toBe('Airport Name');
		});

		it('should handle missing party information gracefully', () => {
			// Create component with empty party list
			const emptyMawbData = { ...mockMawbData, partyList: [] };

			TestBed.resetTestingModule();
			TestBed.configureTestingModule({
				imports: [PreviewAwbComponent],
				providers: [
					{ provide: MatDialogRef, useValue: mockDialogRef },
					{ provide: MAT_DIALOG_DATA, useValue: emptyMawbData },
					{ provide: PdfExportService, useValue: mockPdfExportService },
					{ provide: NotificationService, useValue: mockNotificationService },
					{ provide: TranslateService, useValue: mockTranslateService },
					{ provide: DatePipe, useValue: mockDatePipe },
					{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
					provideTranslateService(),
				],
			});

			const emptyFixture = TestBed.createComponent(PreviewAwbComponent);
			const emptyComponent = emptyFixture.componentInstance;
			emptyFixture.detectChanges();

			expect(emptyComponent.shipperInfo).toBeNull();
			expect(emptyComponent.consigneeInfo).toBeNull();
			expect(emptyComponent.carrierInfo).toBeNull();
			expect(emptyComponent.issuedByInfo).toBeNull();
		});

		it('should handle missing accounting notes gracefully', () => {
			// Create component with empty accounting note list
			const emptyMawbData = { ...mockMawbData, accountingNoteList: [] };

			TestBed.resetTestingModule();
			TestBed.configureTestingModule({
				imports: [PreviewAwbComponent],
				providers: [
					{ provide: MatDialogRef, useValue: mockDialogRef },
					{ provide: MAT_DIALOG_DATA, useValue: emptyMawbData },
					{ provide: PdfExportService, useValue: mockPdfExportService },
					{ provide: NotificationService, useValue: mockNotificationService },
					{ provide: TranslateService, useValue: mockTranslateService },
					{ provide: DatePipe, useValue: mockDatePipe },
					{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
					provideTranslateService(),
				],
			});

			const emptyFixture = TestBed.createComponent(PreviewAwbComponent);
			const emptyComponent = emptyFixture.componentInstance;
			emptyFixture.detectChanges();

			expect(emptyComponent.shipperAccountingNote).toBe('');
			expect(emptyComponent.consigneeAccountingNote).toBe('');
			expect(emptyComponent.carrierAccountingNote).toBe('');
			expect(emptyComponent.issuedByAccountingNote).toBe('');
		});

		it('should return early when no data is provided', () => {
			// Create component with null data - but we need to provide a minimal structure to avoid template errors
			const minimalData = {
				textualHandlingInstructions: '',
				waybillPrefix: '',
				waybillNumber: '',
				houseWaybills: [],
				partyList: [],
				accountingNoteList: [],
				departureLocation: '',
				arrivalLocation: '',
				requestedFlight: '',
				requestedDate: '',
				toFirst: '',
				toSecond: '',
				toThird: '',
				byFirstCarrier: '',
				bySecondCarrier: '',
				byThirdCarrier: '',
				insuredAmount: null,
				carrierChargeCode: '',
				weightValuationIndicator: '',
				otherChargesIndicator: '',
				declaredValueForCarriage: null,
				declaredValueForCustoms: null,
				totalGrossWeight: 0,
				serviceCode: '',
				rateClassCode: '',
				totalVolumetricWeight: 0,
				rateCharge: null,
				goodsDescription: '',
				otherChargeList: [],
				destinationCurrencyRate: 0,
				destinationCharges: null,
				shippingInfo: '',
				shippingRefNo: '',
				carrierDeclarationDate: '',
				carrierDeclarationPlace: '',
				consignorDeclarationSignature: '',
				carrierDeclarationSignature: '',
				total: null,
				destinationCollectCharges: null,
				totalCollectCharges: null,
				prepaidFormData: {},
				airPortInfoComponent: mockAirportInfoComponent,
			};

			TestBed.resetTestingModule();
			TestBed.configureTestingModule({
				imports: [PreviewAwbComponent],
				providers: [
					{ provide: MatDialogRef, useValue: mockDialogRef },
					{ provide: MAT_DIALOG_DATA, useValue: minimalData },
					{ provide: PdfExportService, useValue: mockPdfExportService },
					{ provide: NotificationService, useValue: mockNotificationService },
					{ provide: TranslateService, useValue: mockTranslateService },
					{ provide: DatePipe, useValue: mockDatePipe },
					{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
					provideTranslateService(),
				],
			});

			const nullFixture = TestBed.createComponent(PreviewAwbComponent);
			const nullComponent = nullFixture.componentInstance;
			nullFixture.detectChanges();

			expect(nullComponent.shipperInfo).toBeNull();
			expect(nullComponent.consigneeInfo).toBeNull();
			expect(nullComponent.carrierInfo).toBeNull();
			expect(nullComponent.issuedByInfo).toBeNull();
		});
	});

	describe('onClose', () => {
		it('should close dialog with true value', () => {
			component.onClose();
			expect(mockDialogRef.close).toHaveBeenCalledWith(true);
		});
	});

	describe('onExport', () => {
		beforeEach(() => {
			// Mock DOM element
			const mockElement = document.createElement('div');
			mockElement.className = 'overlap-wrapper';
			spyOn(document, 'querySelector').and.returnValue(mockElement);
		});

		it('should export PDF successfully', async () => {
			mockPdfExportService.exportToPdf.and.returnValue(Promise.resolve());

			await component.onExport();

			expect(component.isExporting).toBeFalse();
			expect(mockNotificationService.showProgress).toHaveBeenCalledWith('mawb.exportpdf.generating');
			expect(mockPdfExportService.exportToPdf).toHaveBeenCalled();
			expect(mockNotificationService.showSuccess).toHaveBeenCalledWith('mawb.exportpdf.success');
		});

		it('should prevent multiple simultaneous exports', async () => {
			component.isExporting = true;

			await component.onExport();

			expect(mockPdfExportService.exportToPdf).not.toHaveBeenCalled();
		});

		it('should handle missing AWB element error', async () => {
			(document.querySelector as jasmine.Spy).and.returnValue(null);

			await component.onExport();

			expect(mockNotificationService.showError).toHaveBeenCalledWith('mawb.exportpdf.failed: Error: AWB content not found');
			expect(component.isExporting).toBeFalse();
		});

		it('should handle PDF export errors', async () => {
			const errorMessage = 'Export failed';
			mockPdfExportService.exportToPdf.and.returnValue(Promise.reject(new Error(errorMessage)));

			await component.onExport();

			expect(mockNotificationService.showError).toHaveBeenCalledWith('mawb.exportpdf.failed: Error: Export failed');
			expect(component.isExporting).toBeFalse();
		});

		it('should use correct filename format', async () => {
			mockPdfExportService.exportToPdf.and.returnValue(Promise.resolve());
			// The component uses the actual DatePipe, not the mock, so we need to test the actual behavior
			const today = new Date();
			const expectedDate =
				today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');

			await component.onExport();

			expect(mockPdfExportService.exportToPdf).toHaveBeenCalledWith(
				jasmine.any(HTMLElement),
				`AWB_${expectedDate}`,
				jasmine.any(Object)
			);
		});

		it('should use AWB-specific PDF options', async () => {
			mockPdfExportService.exportToPdf.and.returnValue(Promise.resolve());

			await component.onExport();

			expect(mockPdfExportService.getAwbPdfOptions).toHaveBeenCalled();
			expect(mockPdfExportService.exportToPdf).toHaveBeenCalledWith(jasmine.any(HTMLElement), jasmine.any(String), {
				format: 'a4',
				orientation: 'landscape',
				quality: 0.95,
				scale: 2,
				margin: 5,
			});
		});
	});
});
