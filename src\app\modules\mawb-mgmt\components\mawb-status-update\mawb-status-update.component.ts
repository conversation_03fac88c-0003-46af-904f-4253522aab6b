import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { MawbStatusService } from '../../services/mawb-status.service';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { EventTimeType, MAWBEventObj } from '../../models/mawb-event.model';
import { MatRadioModule } from '@angular/material/radio';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { MatCheckboxChange, MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatNativeDateModule, provideNativeDateAdapter } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatDividerModule } from '@angular/material/divider';
import { DatePipe } from '@angular/common';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';

const DATE_FORMAT = 'yyyy-MM-dd HH:mm:ss';
export interface UpdateDialog {
	param: MAWBEventObj;
}

@Component({
	selector: 'orll-mawb-status-update',
	imports: [
		MatRadioModule,
		FormsModule,
		MatCheckboxModule,
		MatFormFieldModule,
		ReactiveFormsModule,
		MatDialogModule,
		TranslateModule,
		MatIconModule,
		MatButtonModule,
		MatDatepickerModule,
		MatNativeDateModule,
		MatInputModule,
		MatDividerModule,
		SpinnerComponent,
	],
	providers: [provideNativeDateAdapter(), DatePipe],
	templateUrl: './mawb-status-update.component.html',
	styleUrl: './mawb-status-update.component.scss',
})
export class MawbStatusUpdateComponent extends DestroyRefComponent implements OnInit {
	events: string[] = [];
	maxDate: Date;

	selectedEvent = '';
	partialEventIndicator = false;
	eventTimeType = EventTimeType[0];
	milestoneTime: Date | null = null;

	dataLoading = false;
	selectedEventsList = [];

	constructor(
		public readonly statusService: MawbStatusService,
		private readonly cdr: ChangeDetectorRef,
		private readonly datePipe: DatePipe,
		public dialogRef: MatDialogRef<MawbStatusUpdateComponent>,
		@Inject(MAT_DIALOG_DATA) public data: UpdateDialog
	) {
		super();
		const today = new Date();
		this.maxDate = new Date();
		this.maxDate.setDate(today.getDate() - 1);
	}

	ngOnInit(): void {
		this.dataLoading = true;
		this.statusService.getEventList(this.data.param).subscribe((res) => {
			this.dataLoading = false;
			this.events = res;
		});
	}

	onEventChange(val: string) {
		this.selectedEvent = val;
	}

	onEventTypeChange(event: MatCheckboxChange) {
		this.eventTimeType = event.checked ? EventTimeType[1] : EventTimeType[0];
	}

	updateEventStatus() {
		this.dataLoading = true;
		const updateTime = this.milestoneTime || this.maxDate;
		const param = {
			...this.data.param,
			eventTimeType: this.eventTimeType,
			status: this.selectedEvent,
			updateTime: this.datePipe.transform(updateTime, DATE_FORMAT),
			partialEventIndicator: this.partialEventIndicator,
		};
		this.statusService
			.updateEventStatus(param)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: () => {
					this.dataLoading = true;
					this.dialogRef.close();
				},
				error: () => {
					this.dataLoading = false;
					this.dialogRef.close();
				},
			});
	}
}
