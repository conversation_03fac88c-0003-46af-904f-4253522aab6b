import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { ShareDialogComponent } from './share-dialog.component';
import { MatTableModule } from '@angular/material/table';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSortModule } from '@angular/material/sort';
import { AutocompleteComponent } from '../autocomplete/autocomplete.component';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { By } from '@angular/platform-browser';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { Organization } from '@shared/models/organization.model';
import { UserProfileService } from '@shared/services/user-profile.service';
import { noop, of, throwError } from 'rxjs';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { DebugElement } from '@angular/core';
import { ShareType } from '@shared/models/share-type.model';
import { ShareDataService } from '@shared/services/share/share-data.service';

describe('ShareDialogComponent', () => {
	let component: ShareDialogComponent;
	let fixture: ComponentFixture<ShareDialogComponent>;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;
	const mockDialogRef = jasmine.createSpyObj(['close']);
	const mockDialogData = {
		title: 'Share SLI',
		shareType: ShareType.SLI,
		param: '1',
	};
	let inputEl: DebugElement;

	// Mock data
	const mockRecords: Organization[] = [
		{
			id: '1',
			name: 'AAA',
			orgType: 'APT',
		},
		{
			id: '2',
			name: 'BBB',
			orgType: 'AIR',
		},
		{
			id: '3',
			name: 'CCC',
			orgType: 'AIR',
		},
	];
	const mockFilterData: Organization[] = [
		{
			id: '2',
			name: 'BBB',
			orgType: 'AIR',
		},
		{
			id: '3',
			name: 'CCC',
			orgType: 'AIR',
		},
	];
	const mockOrgTypes: string[] = ['APT', 'AIR'];
	const mockOrgMgtService = {
		getOrgList: jasmine.createSpy('getOrgList').and.returnValue(of(mockRecords)),
		getOptions: jasmine.createSpy('getOptions').and.returnValue(of(mockOrgTypes)),
	};

	mockOrgMgtService.getOrgList.and.callFake((arg?: string) => {
		if (arg) {
			if (arg === 'error') {
				return throwError(() => new Error('backend error'));
			}
			return of(mockFilterData);
		} else {
			return of(mockRecords);
		}
	});

	const mockShareDataService = {
		shareData: jasmine.createSpy('shareData').and.returnValue(of('')),
	};

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [
				ShareDialogComponent,
				AutocompleteComponent,
				MatIconModule,
				ReactiveFormsModule,
				MatTableModule,
				MatSortModule,
				MatDialogModule,
				MatCheckboxModule,
				MatButtonModule,
				TranslateModule.forRoot(),
			],
			providers: [
				{
					provide: UserProfileService,
					useValue: userProfileServiceSpy,
				},
				{
					provide: OrgMgmtRequestService,
					useValue: mockOrgMgtService,
				},
				{ provide: MatDialogRef, useValue: mockDialogRef },
				{ provide: MAT_DIALOG_DATA, useValue: mockDialogData },
				{ provide: ShareDataService, useValue: mockShareDataService },
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		}).compileComponents();
	});

	beforeEach(() => {
		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));

		fixture = TestBed.createComponent(ShareDialogComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
		inputEl = fixture.debugElement.query(By.css('.mat-mdc-input-element'));
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize the sharedOrgSearchForm', () => {
		expect(component.sharedOrgSearchForm).toBeDefined();
	});

	it('onReset() should reset all the form fields and selected items', () => {
		component.selectedOrgType = 'AIR';
		component.onReset({ preventDefault: noop, stopPropagation: noop } as any);
		expect(component.selectedOrgType).toBeNull();
	});

	it('should render table with data', () => {
		const compiled = fixture.nativeElement as HTMLElement;
		const rows = compiled.querySelectorAll('tbody tr');
		expect(rows.length).toBe(3);
	});

	it('should show 2 options when input is focused and panel opened', fakeAsync(() => {
		inputEl.nativeElement.click();
		fixture.detectChanges();
		tick(200);
		const options = document.querySelectorAll('.mat-mdc-option');
		expect(options.length).toBe(2);
	}));

	it('should allow sorting by orgType column', () => {
		const headerCells = fixture.debugElement.queryAll(By.css('.mat-sort-header'));
		const typeHeader = headerCells.find((h) => h.nativeElement.textContent.includes('orgType'));

		typeHeader?.triggerEventHandler('click', null);
		fixture.detectChanges();

		const firstRow = fixture.nativeElement.querySelector('tbody tr td:nth-child(3)');
		expect(firstRow.textContent).toContain('AIR');
	});

	it('should select a row when checkbox is clicked', () => {
		const checkboxes = fixture.debugElement.queryAll(By.css('mat-checkbox'));
		checkboxes[1].triggerEventHandler('change', { checked: true });
		fixture.detectChanges();

		expect(component.selection.selected.length).toBe(1);
		expect(component.selection.isSelected('1')).toBeTrue();
	});

	it('should toggle all rows when master checkbox is clicked', () => {
		const masterCheckbox = fixture.debugElement.query(By.css('.mat-mdc-header-row .mat-mdc-checkbox'));
		masterCheckbox.triggerEventHandler('change', { checked: true });
		fixture.detectChanges();

		expect(component.selection.selected.length).toBe(3);

		masterCheckbox.triggerEventHandler('change', { checked: false });
		fixture.detectChanges();

		expect(component.selection.selected.length).toBe(0);
	});

	it('onSearch() should filter data when search is called', () => {
		component.selectedOrgType = 'AIR';
		component.onSearch();
		const rows = fixture.nativeElement.querySelectorAll('tbody tr');
		expect(rows.length).toBe(2);
		expect(rows[0].textContent).toContain('AIR');
	});

	it('onSortChange() should sort data', () => {
		component.dataSource.data = mockRecords;
		component.onSortChange({ active: 'orgType', direction: 'asc' });
		const rows = fixture.nativeElement.querySelectorAll('tbody tr');
		expect(rows[0].textContent).toContain('AIR');
	});

	it('should stop loading when getData fails', () => {
		component.selectedOrgType = 'error';

		component.ngOnInit();
		fixture.detectChanges();
		expect(component.dataLoading).toBe(false);
	});

	it('trackById', () => {
		const org: Organization = {
			id: 'org',
			name: 'Test Org',
			orgType: '',
		};
		const result = component.trackByOrgId(0, org);
		expect(result).toBe('org');
	});

	describe('#isAllSelected', () => {
		it('should return true when there is no data (0 selected == 0 rows)', () => {
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return false when there are data but nothing is selected', () => {
			component.dataSource.data = mockRecords;
			component.selection.deselect('1', '2', '3');
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return true when all rows are selected', () => {
			component.dataSource.data = mockRecords;
			component.selection.select('1', '2', '3');
			expect(component.isAllSelected()).toBe(true);
		});

		it('should return false when only some rows are selected', () => {
			component.dataSource.data = mockRecords;
			component.selection.select('1');
			expect(component.isAllSelected()).toBe(false);
		});
	});
});
