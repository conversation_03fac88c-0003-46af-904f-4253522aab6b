.other-charges-list {
	height: 170px;
	margin-top: 40px;
	overflow-y: auto;

	border: 1px solid var(--iata-grey-100);

	border-radius: 6px;

	> .row {
		width: 100%;
		padding: 12px;
		border-bottom: 1px solid var(--iata-grey-100);
	}

	.label-name {
		margin-right: 4px;
	}

	.col-charge_payment_type {
		width: 240px;
		flex: 0 0 240px;
	}

	.col-entitlement {
		width: 140px;
		flex: 0 0 140px;
	}

	.col-delete {
		width: 60px;
		flex: 0 0 60px;
		display: inline-flex;
		align-items: center;
	}

	.cell-content {
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

// Common variables
$white: #ffffff;
$border-color: #999999;
$text-color: #333333;
$light-gray: #f2f2f2;
$border-style: 1px solid;
$box-shadow:
	0px 8px 18px #00000014,
	0px 4px 4px #0000000a;

// Base component styles with nested selectors to reduce repetition
.element-proview-AWB {
	background-color: $white;
	display: flex;
	flex-direction: row;
	justify-content: center;
	width: 100%;
	font-weight: 400;
	color: $text-color;
	font-size: 14px;
	letter-spacing: 0;

	.group-wrapper {
		background-color: $white;
		width: 1520px;
		height: 2590px;
	}

	.group {
		position: relative;
		height: 2590px;
		background-color: $white;
		box-shadow: $box-shadow;
	}
}

.element-proview-AWB {
	// Button footer styles
	.button-footer {
		position: relative;
		display: flex;
		justify-content: flex-end;
		margin-top: 80px;
		margin-right: 80px;

		.close-button {
			margin-right: 20px;
		}
	}

	// Overlap container styles
	.overlap-wrapper {
		position: relative;
		width: 1323px;
		height: 2286px;
		top: 54px;
		left: 113px;
	}

	.overlap {
		position: relative;
		height: 2286px;
	}
}

// Consolidated mixins for efficiency
@mixin base-styles {
	position: absolute;
	background-color: $white;
	border: $border-style;
	border-color: $border-color;
}

@mixin text-style {
	font-weight: 400;
	color: $text-color;
	letter-spacing: 0;
	white-space: nowrap;
}

@mixin positioned-element($width, $height, $top, $left) {
	@include base-styles;
	width: $width;
	height: $height;
	top: $top;
	left: $left;
}

@mixin vertical-divider($top, $left, $height) {
	@include positioned-element(1px, $height, $top, $left);
}

.element-proview-AWB {
	.div {
		@include vertical-divider(0, 73px, 54px);
	}

	.group-2 {
		@include vertical-divider(0, 145px, 54px);
	}
}

// Consolidated text styles mixin
@mixin text-element($top, $left, $font-size: 12px, $font-weight: 400, $color: $text-color) {
	position: absolute;
	top: $top;
	left: $left;
	font-weight: $font-weight;
	color: $color;
	font-size: $font-size;
	letter-spacing: 0;
	line-height: 14px;
	height: 14px;
	white-space: nowrap;
}

// Text wrapper mixin for consistent text styling
@mixin text-wrapper($top, $left, $font-size: 12px, $font-weight: 400) {
	@include text-element($top, $left, $font-size, $font-weight);
}

@mixin text-element-24($top, $left, $font-size: 14px, $font-weight: 400, $text-align: left, $color: $text-color) {
	position: absolute;
	top: $top;
	left: $left;
	font-weight: $font-weight;
	color: $color;
	font-size: $font-size;
	letter-spacing: 0;
	line-height: 24px;
	height: 24px;
	white-space: nowrap;
	@if $text-align != left {
		text-align: $text-align;
	}
}

.element-proview-AWB {
	.overlap-group-wrapper {
		@include positioned-element(664px, 216px, 53px, 0);
	}

	.overlap-group {
		position: relative;
		height: 216px;
	}

	.rectangle {
		@include positioned-element(662px, 216px, 0, 0);
	}

	.text-wrapper {
		@include text-element(16px, 16px);
	}

	.div-wrapper {
		@include positioned-element(333px, 72px, 0, 331px);
	}
}

.element-proview-AWB {
	// Content wrapper styles with modifiers
	.party-content-wrapper {
		position: absolute;
		height: auto;
		top: 90px;
		left: 24px;
		right: 24px;
		line-height: 20px;
		word-break: break-word;
		white-space: normal;

		&.carrier-agent {
			top: 60px;
		}
	}

	// Item wrapper styles with modifiers
	.item-wrapper {
		position: absolute;
		height: auto;
		top: 40px;
		left: 16px;
		right: 16px;
		line-height: 14px;
		word-break: break-word;
		white-space: normal;

		&.center {
			text-align: center;
		}

		&.shipper-consignee {
			left: 40px;
		}

		&.charge-code {
			left: 8px;
			right: 0;
		}

		&.charge-weight {
			top: 30px;
			text-align: center;
		}

		&.weight-unit {
			top: 30px;
			left: 4px;
		}

		&.rate-class {
			top: 102px;
			left: 5px;
		}

		&.signature {
			top: 0;
			margin-top: -15px;
			text-align: center;

			&.date {
				margin-left: 120px;
				text-align: left;
			}

			&.at-place {
				margin-right: 60px;
			}

			&.carrier {
				margin-right: 150px;
				text-align: right;
			}
		}
	}
}

// Consolidated positioning mixins
@mixin relative-positioned-box($width, $height, $bg-color: $white) {
	position: relative;
	width: $width;
	height: $height;
	background-color: $bg-color;
	border: $border-style;
	border-color: $border-color;
}

.element-proview-AWB {
	.overlap-group-2 {
		@include relative-positioned-box(331px, 72px);
	}

	.text-wrapper-2 {
		@include text-element(15px, 40px);
	}

	.group-3 {
		@include positioned-element(664px, 216px, 268px, 0);
	}

	.consignee-s-name-and-wrapper {
		@include relative-positioned-box(331px, 72px, $light-gray);
	}

	.group-4 {
		@include positioned-element(1323px, 2233px, 53px, 0);
	}
}

.element-proview-AWB {
	.overlap-2 {
		position: relative;
		width: 1329px;
		height: 2233px;
	}

	.group-5 {
		@include positioned-element(668px, 162px, 0, 661px);
	}

	.overlap-group-3 {
		@include relative-positioned-box(662px, 162px);
	}

	// Text wrapper elements with consistent styling but different positions
	.text-wrapper-3 {
		@include text-element(15px, 15px);
	}

	.text-wrapper-4 {
		@include text-element(72px, 15px, 20px, 500);
	}

	.text-wrapper-5 {
		@include text-element(129px, 15px, 14px);
	}
}

.element-proview-AWB {
	// Group elements with consistent positioning pattern
	.group-6 {
		@include positioned-element(664px, 144px, 430px, 0);
	}

	.issuing-carrier-s-wrapper {
		@include relative-positioned-box(662px, 144px);
	}

	.group-7 {
		@include positioned-element(1325px, 144px, 857px, 0);
	}

	.overlap-3 {
		@include relative-positioned-box(1323px, 144px);
	}

	.group-8 {
		@include positioned-element(664px, 215px, 430px, 661px);
	}

	.overlap-4 {
		@include relative-positioned-box(662px, 215px);
	}
}

.element-proview-AWB {
	// Group elements with consistent positioning pattern
	.group-9 {
		@include positioned-element(822px, 214px, 1573px, 503px);
	}

	.overlap-5 {
		@include relative-positioned-box(820px, 214px);
	}

	.group-10 {
		@include positioned-element(822px, 214px, 1786px, 503px);
	}

	.group-11 {
		@include positioned-element(555px, 43px, 141px, 129px);
	}

	.p {
		@include text-element(29px, 186px);
	}
}

.element-proview-AWB {
	// Vector and text elements
	.vector {
		position: absolute;
		width: 553px;
		height: 2px;
		top: 0;
		left: 0;
		object-fit: cover;
	}

	// Text content with consistent styling
	.shipper-certifies {
		position: absolute;
		width: 553px;
		height: 42px;
		top: 50px;
		left: 129px;
		font-weight: 400;
		color: $text-color;
		font-size: 12px;
		letter-spacing: 0;
		line-height: 14px;
	}

	.span {
		font-weight: 400;
		color: $text-color;
		font-size: 12px;
		letter-spacing: 0;
		line-height: 14px;
	}

	.text-wrapper-6 {
		font-weight: 500;
	}

	// Group with border and background
	.group-12 {
		@include positioned-element(820px, 143px, 1999px, 503px);
	}
}

.element-proview-AWB {
	// Group element with relative positioning
	.group-13 {
		position: relative;
		width: 824px;
		height: 42px;
		top: 70px;
	}

	// Mixin for centered text wrappers
	@mixin centered-text-wrapper($left) {
		@include text-element(28px, $left);
		text-align: center;
	}

	// Text wrappers with consistent styling but different horizontal positions
	.text-wrapper-7 {
		@include centered-text-wrapper(125px);
	}

	.text-wrapper-8 {
		@include centered-text-wrapper(356px);
	}

	.text-wrapper-9 {
		@include centered-text-wrapper(544px);
	}
}

.element-proview-AWB {
	// Vector element
	.vector-2 {
		position: absolute;
		width: 818px;
		height: 1px;
		top: 0;
		left: 0;
	}

	// Group elements with consistent positioning
	.group-14 {
		@include positioned-element(664px, 217px, 214px, 661px);
	}

	.it-is-agreed-that-wrapper {
		@include relative-positioned-box(662px, 217px);
	}

	// Text content with consistent styling
	.it-is-agreed-that {
		position: absolute;
		width: 547px;
		height: 126px;
		top: 45px;
		left: 57px;
		font-weight: 400;
		color: $text-color;
		font-size: 12px;
		letter-spacing: 0.24px;
		line-height: 14px;
	}

	.group-15 {
		@include positioned-element(664px, 54px, 161px, 661px);
	}
}

.element-proview-AWB {
	// Overlap and text elements
	.overlap-6 {
		@include relative-positioned-box(662px, 54px);
	}

	.text-wrapper-10 {
		@include text-element(19px, 15px);
	}

	.group-16 {
		@include positioned-element(334px, 72px, 573px, 0);
	}

	.overlap-7 {
		@include relative-positioned-box(332px, 72px);
	}
}

.element-proview-AWB .group-17 {
	position: absolute;
	width: 664px;
	height: 72px;
	top: 644px;
	left: 0;
}

.element-proview-AWB .overlap-8 {
	position: relative;
	width: 662px;
	height: 72px;
	background-color: $white;
	border: $border-style;
	border-color: $border-color;
}

.element-proview-AWB .group-18 {
	position: absolute;
	width: 74px;
	height: 72px;
	top: 715px;
	left: 0;
}

.element-proview-AWB .overlap-group-4 {
	position: relative;
	width: 72px;
	height: 72px;
	background-color: $white;
	border: $border-style;
	border-color: $border-color;
}

.element-proview-AWB .group-19 {
	position: absolute;
	width: 72px;
	height: 574px;
	top: 1000px;
	left: 0;
}

.element-proview-AWB .overlap-9 {
	position: relative;
	width: 74px;
	height: 574px;
}

.element-proview-AWB .group-20 {
	position: absolute;
	width: 74px;
	height: 72px;
	top: 0;
	left: 0;
}

.element-proview-AWB .text-wrapper-11 {
	position: absolute;
	width: 37px;
	height: 42px;
	top: 14px;
	left: 17px;
	font-weight: 400;
	color: $text-color;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 14px;
}

.element-proview-AWB .group-21 {
	position: absolute;
	width: 72px;
	height: 432px;
	top: 71px;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-22 {
	position: absolute;
	width: 72px;
	height: 72px;
	top: 502px;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-23 {
	position: absolute;
	width: 18px;
	height: 574px;
	top: 1000px;
	left: 196px;
}

.element-proview-AWB .overlap-10 {
	position: relative;
	width: 20px;
	height: 574px;
}

.element-proview-AWB .group-24 {
	width: 20px;
	height: 72px;
	position: absolute;
	top: 0;
	left: 0;
}

.element-proview-AWB .KG-lb-wrapper {
	position: relative;
	width: 18px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .KG-lb {
	position: absolute;
	height: 20px;
	top: 25px;
	left: 3px;
	font-weight: 400;
	color: $text-color;
	font-size: 8px;
	letter-spacing: 0;
	line-height: normal;
}

.element-proview-AWB .group-25 {
	position: absolute;
	width: 18px;
	height: 503px;
	top: 71px;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-26 {
	position: absolute;
	width: 18px;
	height: 574px;
	top: 1000px;
	left: 213px;
	background-color: $light-gray;
	border: $border-style;
	border-color: $border-color;
}

.element-proview-AWB .group-27 {
	position: absolute;
	width: 18px;
	height: 574px;
	top: 1000px;
	left: 372px;
	background-color: $light-gray;
	border: $border-style;
	border-color: $border-color;
}

.element-proview-AWB .group-28 {
	position: absolute;
	width: 18px;
	height: 574px;
	top: 1000px;
	left: 514px;
	background-color: $light-gray;
	border: $border-style;
	border-color: $border-color;
}

.element-proview-AWB .group-29 {
	position: absolute;
	width: 18px;
	height: 574px;
	top: 1000px;
	left: 674px;
	background-color: $light-gray;
	border: $border-style;
	border-color: $border-color;
}

.element-proview-AWB .group-30 {
	position: absolute;
	width: 18px;
	height: 574px;
	top: 1000px;
	left: 898px;
	background-color: $light-gray;
	border: $border-style;
	border-color: $border-color;
}

.element-proview-AWB .group-31 {
	position: absolute;
	width: 143px;
	height: 574px;
	top: 1000px;
	left: 230px;
}

.element-proview-AWB .overlap-11 {
	position: relative;
	width: 145px;
	height: 574px;
}

.element-proview-AWB .group-32 {
	width: 145px;
	height: 574px;
	position: absolute;
	top: 0;
	left: 0;
}

.element-proview-AWB .overlap-group-5 {
	position: relative;
	width: 143px;
	height: 574px;
	background-size: 100% 100%;
}

.element-proview-AWB .text-wrapper-12 {
	top: 9px;
	left: 17px;
	position: absolute;
	height: 14px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

// Rectangle mixin for positioned rectangles with borders
@mixin rectangle-box {
	position: absolute;
	background-color: $white;
	border: $border-style;
	border-color: $border-color;
}

@mixin rectangle($width, $height, $top, $left) {
	@include rectangle-box;
	width: $width;
	height: $height;
	top: $top;
	left: $left;
}

// Group positioning mixin for consistent group element styling
@mixin group-positioning {
	position: absolute;
	background-color: $white;
}

@mixin group-element($width, $height, $top, $left) {
	@include group-positioning;
	width: $width;
	height: $height;
	top: $top;
	left: $left;
}

// Relative box mixin for relative positioned elements
@mixin relative-box($width, $height) {
	position: relative;
	width: $width;
	height: $height;
	background-color: $white;
	border: $border-style;
	border-color: $border-color;
}

.element-proview-AWB .rectangle-2 {
	@include rectangle(126px, 36px, 0, 0);
}

.element-proview-AWB .rectangle-3 {
	@include rectangle(126px, 503px, 35px, 0);
}

.element-proview-AWB .overlap-12 {
	@include relative-box(126px, 538px);
}

.element-proview-AWB .text-wrapper-13 {
	@include text-wrapper(11px, 7px);
}

.element-proview-AWB .group-33 {
	@include group-element(128px, 538px, 36px, 17px);
}

.element-proview-AWB .group-34 {
	@include group-element(126px, 574px, 1000px, 389px);
}

.element-proview-AWB .group-35 {
	width: 128px;
	height: 574px;
	// This element doesn't have position properties, so we don't apply group-element mixin
}

.element-proview-AWB .overlap-group-6 {
	@include relative-box(126px, 574px);
}

.element-proview-AWB .rectangle-4 {
	@include rectangle(126px, 72px, 0, 0);
}

.element-proview-AWB .rectangle-5 {
	@include rectangle(126px, 503px, 71px, 0);
}

.element-proview-AWB .chargeable-weight {
	position: absolute;
	height: 28px;
	top: 22px;
	left: 32px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 14px;
}

.element-proview-AWB .group-36 {
	position: absolute;
	width: 144px;
	height: 574px;
	top: 1000px;
	left: 531px;
}

.element-proview-AWB .group-37 {
	width: 146px;
	height: 574px;
}

.element-proview-AWB .overlap-group-7 {
	position: relative;
	width: 144px;
	height: 574px;
}

@mixin centered-text-wrapper($top, $left) {
	@include text-wrapper($top, $left);
	text-align: center;
}

.element-proview-AWB .rectangle-6 {
	@include rectangle(144px, 72px, 0, 0);
}

.element-proview-AWB .text-wrapper-14 {
	@include centered-text-wrapper(29px, 38px);
}

.element-proview-AWB .rectangle-7 {
	@include rectangle(144px, 503px, 71px, 0);
}

.element-proview-AWB .group-38 {
	@include group-element(208px, 574px, 1000px, 691px);
}

.element-proview-AWB .group-39 {
	width: 210px;
	height: 574px;
	// This element doesn't have position properties, so we don't apply group-element mixin
}

.element-proview-AWB .overlap-group-8 {
	@include relative-box(208px, 574px);
}

.element-proview-AWB .rectangle-8 {
	@include rectangle(208px, 72px, 0, 0);
}

.element-proview-AWB .text-wrapper-15 {
	@include centered-text-wrapper(29px, 91px);
}

.element-proview-AWB .rectangle-9 {
	@include rectangle(208px, 432px, 71px, 0);
}

.element-proview-AWB .rectangle-10 {
	@include rectangle(208px, 72px, 502px, 0);
}

.element-proview-AWB .group-40 {
	position: absolute;
	width: 408px;
	height: 574px;
	top: 1000px;
	left: 915px;
}

.element-proview-AWB .group-41 {
	width: 410px;
	height: 574px;
}

.element-proview-AWB .overlap-group-9 {
	position: relative;
	width: 408px;
	height: 574px;
}

.element-proview-AWB .rectangle-11 {
	position: absolute;
	width: 408px;
	height: 60px;
	top: 0;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-12 {
	width: 408px;
	height: 515px;
	top: 59px;
	position: absolute;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .nature-and-quantity {
	position: absolute;
	height: 28px;
	top: 16px;
	left: 85px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 14px;
}

.element-proview-AWB .group-42 {
	position: absolute;
	width: 126px;
	height: 574px;
	top: 1000px;
	left: 71px;
}

.element-proview-AWB .overlap-13 {
	position: relative;
	width: 128px;
	height: 574px;
}

.element-proview-AWB .group-43 {
	width: 128px;
	height: 72px;
	position: absolute;
	top: 0;
	left: 0;
}

.element-proview-AWB .overlap-group-10 {
	position: relative;
	width: 126px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-16 {
	position: absolute;
	width: 42px;
	height: 28px;
	top: 21px;
	left: 41px;

	font-weight: 400;
	color: #333333;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 14px;
}

.element-proview-AWB .group-44 {
	position: absolute;
	width: 126px;
	height: 432px;
	top: 71px;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-45 {
	position: absolute;
	width: 126px;
	height: 72px;
	top: 502px;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-46 {
	position: absolute;
	width: 59px;
	height: 72px;
	top: 715px;
	left: 605px;
}

.element-proview-AWB .overlap-14 {
	position: relative;
	width: 57px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-47 {
	position: absolute;
	width: 79px;
	height: 72px;
	top: 715px;
	left: 531px;
}

.element-proview-AWB .overlap-15 {
	position: relative;
	width: 75px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-17 {
	position: absolute;
	height: 14px;
	top: 45px;
	left: 9px;

	font-weight: 400;
	color: transparent;
	font-size: 14px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-48 {
	position: absolute;
	width: 78px;
	height: 72px;
	top: 715px;
	left: 757px;
}

.element-proview-AWB .overlap-16 {
	position: relative;
	width: 72px;
	height: 72px;
}

.element-proview-AWB .rectangle-13 {
	width: 72px;
	height: 27px;
	top: 0;
	left: 0;
	background-color: #ffffff;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-14 {
	width: 37px;
	height: 46px;
	top: 26px;
	left: 0;
	background-color: #ffffff;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-15 {
	width: 36px;
	height: 46px;
	top: 26px;
	left: 36px;
	background-color: #ffffff;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB {
	.text-wrapper-18 {
		@include text-element-24(37px, 7px, 12px, 400, center);
	}

	.text-wrapper-19 {
		@include text-element-24(37px, 38px, 12px, 400, center);
	}
}

.element-proview-AWB {
	.text-wrapper-20 {
		@include text-element(7px, 14px);
	}
}

.element-proview-AWB .group-49 {
	position: absolute;
	width: 78px;
	height: 72px;
	top: 715px;
	left: 828px;
}

.element-proview-AWB {
	.text-wrapper-21 {
		@include text-element(7px, 20px);
	}
}

.element-proview-AWB .group-50 {
	position: absolute;
	width: 514px;
	height: 72px;
	top: 1573px;
	left: 0;
}

.element-proview-AWB .overlap-17 {
	position: relative;
	width: 504px;
	height: 72px;
}

.element-proview-AWB .rectangle-16 {
	position: absolute;
	width: 253px;
	height: 72px;
	top: 0;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-17 {
	background-color: #ffffff;
	position: absolute;
	width: 252px;
	height: 72px;
	top: 0;
	left: 252px;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-18 {
	width: 128px;
	height: 27px;
	top: 0;
	position: absolute;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-19 {
	width: 126px;
	height: 27px;
	top: 0;
	left: 378px;
	background-color: #ffffff;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-20 {
	width: 252px;
	height: 27px;
	top: 0;
	left: 127px;
	background-color: #ffffff;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB {
	// Text wrappers with 24px height and line-height
	.text-wrapper-24 {
		@include text-element-24(2px, 40px);
	}

	.text-wrapper-25 {
		@include text-element-24(2px, 418px);
	}
}

.element-proview-AWB {
	.text-wrapper-26 {
		@include text-element-24(2px, 206px, 14px, 400, center);
	}
}

.element-proview-AWB .group-51 {
	position: absolute;
	width: 510px;
	height: 72px;
	top: 1644px;
	left: 0;
}

.element-proview-AWB {
	.text-wrapper-27 {
		@include text-element-24(0, 199px, 14px, 400, center);
	}
}

.element-proview-AWB .group-52 {
	position: absolute;
	width: 510px;
	height: 72px;
	top: 1715px;
	left: 0;
}

.element-proview-AWB {
	.text-wrapper-28 {
		@include text-element-24(2px, 242px, 14px, 400, center);
	}
}

.element-proview-AWB .group-53 {
	position: absolute;
	width: 510px;
	height: 72px;
	top: 1786px;
	left: 0;
}

.element-proview-AWB {
	.text-wrapper-29 {
		@include text-element-24(2px, 154px, 14px, 400, center);
	}
}

.element-proview-AWB .group-54 {
	position: absolute;
	width: 510px;
	height: 72px;
	top: 1857px;
	left: 0;
}

.element-proview-AWB {
	.text-wrapper-30 {
		@include text-element-24(2px, 151px, 14px, 400, center);
	}
}

.element-proview-AWB .group-55 {
	position: absolute;
	width: 504px;
	height: 72px;
	top: 1928px;
	left: 0;
}

.element-proview-AWB .overlap-18 {
	position: relative;
	height: 72px;
}

.element-proview-AWB .rectangle-21 {
	position: absolute;
	width: 253px;
	height: 72px;
	top: 0;
	left: 0;
	background-color: #f2f2f2;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-22 {
	background-color: #f2f2f2;
	position: absolute;
	width: 252px;
	height: 72px;
	top: 0;
	left: 252px;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-56 {
	position: absolute;
	width: 512px;
	height: 72px;
	top: 1999px;
	left: 0;
}

.element-proview-AWB .rectangle-23 {
	width: 126px;
	left: 64px;
	background-color: #ffffff;
	position: absolute;
	height: 27px;
	top: 0;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-24 {
	position: absolute;
	width: 126px;
	height: 27px;
	top: 0;
	left: 315px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB {
	// Consolidated text wrappers with centered alignment
	.text-wrapper-31 {
		@include text-element-24(0, 85px, 14px, 400, center);
	}

	.text-wrapper-32 {
		@include text-element-24(0, 339px, 14px, 400, center);
	}
}

.element-proview-AWB {
	// Consolidated group elements with positioned-element mixin
	.group-57 {
		@include positioned-element(512px, 72px, 2070px, 0);
		background-color: transparent; // Override default white background
		border: none; // Override default border
	}

	.group-58 {
		@include positioned-element(766px, 92px, 2141px, 0);
		background-color: transparent; // Override default white background
		border: none; // Override default border
	}

	// Consolidated rectangle elements with light gray background
	.rectangle-25 {
		@include positioned-element(222px, 27px, 0, 16px);
		background-color: $light-gray;
	}

	.rectangle-26 {
		@include positioned-element(222px, 27px, 0, 268px);
		background-color: $light-gray;
	}

	// Consolidated text wrappers with centered alignment and slight top offset
	.text-wrapper-33 {
		@include text-element-24(2px, 39px, 14px, 400, center);
	}

	.text-wrapper-34 {
		@include text-element-24(2px, 284px, 14px, 400, center);
	}
}

.element-proview-AWB {
	.overlap-19 {
		@include relative-positioned-box(756px, 92px);
		border: none; // Override default border
	}
}

.element-proview-AWB .rectangle-27 {
	width: 253px;
	height: 72px;
	top: 0;
	left: 503px;
	background-color: #f2f2f2;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-28 {
	width: 222px;
	height: 27px;
	top: 0;
	left: 519px;
	background-color: #f2f2f2;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-35 {
	top: 68px;
	left: 129px;
	color: transparent;
	position: absolute;
	font-weight: 400;
	font-size: 14px;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .text-wrapper-36 {
	top: 68px;
	left: 436px;
	color: transparent;
	position: absolute;
	font-weight: 400;
	font-size: 14px;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB {
	// Special text element with multi-line height
	.for-carrier-s-use {
		@include text-element(12px, 58px, 14px);
		height: 48px; // Double height for multi-line text
		text-align: center;
		line-height: 24px;
	}
}

.element-proview-AWB {
	// Consolidated text wrappers with centered alignment and slight top offset
	.text-wrapper-37 {
		@include text-element-24(2px, 306px, 14px, 400, center);
	}

	.text-wrapper-38 {
		@include text-element-24(2px, 562px, 14px, 400, center);
	}
}

.element-proview-AWB .group-59 {
	position: absolute;
	width: 61px;
	height: 72px;
	top: 715px;
	left: 475px;
}

.element-proview-AWB .text-wrapper-39 {
	top: 45px;
	left: 12px;
	position: absolute;
	height: 14px;
	font-weight: 400;
	color: transparent;
	font-size: 14px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB {
	// Consolidated group and wrapper elements
	.group-60 {
		@include positioned-element(38px, 72px, 715px, 722px);
		background-color: transparent;
		border: none;
	}

	.CHGS-code-wrapper {
		@include relative-positioned-box(36px, 72px);
	}

	.CHGS-code {
		@include text-element(8px, 0, 12px);
		height: 28px;
		text-align: center;
	}

	.group-61 {
		@include positioned-element(219px, 72px, 715px, 899px);
		background-color: transparent;
		border: none;
	}

	.overlap-20 {
		@include relative-positioned-box(217px, 72px);
	}
}

.element-proview-AWB {
	// Consolidated text wrappers with similar styling
	.text-wrapper-40 {
		@include text-element(15px, 11px);
	}

	.group-62 {
		@include positioned-element(210px, 72px, 715px, 1115px);
	}

	.overlap-21 {
		@include relative-positioned-box(208px, 72px);
	}

	.text-wrapper-41 {
		@include text-element(15px, 13px);
	}
}

.element-proview-AWB {
	// Group elements with consistent positioning pattern
	.group-63 {
		@include positioned-element(209px, 72px, 929px, 1116px);
	}

	.overlap-22 {
		@include relative-positioned-box(207px, 72px);
	}

	.text-wrapper-42 {
		@include text-element(15px, 93px);
	}

	// Group elements with consistent height and different widths
	.group-64 {
		@include positioned-element(334px, 72px, 786px, 0);
	}

	.group-65 {
		@include positioned-element(168px, 72px, 786px, 331px);
	}

	.overlap-23 {
		@include relative-positioned-box(166px, 72px);
	}
}

.element-proview-AWB {
	// Group elements with consistent height and different widths at same top position
	.group-66 {
		@include positioned-element(168px, 72px, 786px, 496px);
	}

	.group-67 {
		@include positioned-element(200px, 72px, 786px, 661px);
	}

	.overlap-24 {
		@include relative-positioned-box(198px, 72px);
	}

	.group-68 {
		@include positioned-element(467px, 72px, 786px, 858px);
	}

	.INSURANCE-if-carrier-wrapper {
		@include relative-positioned-box(465px, 72px);
	}
}

.element-proview-AWB {
	// Multi-line text element with custom width
	.INSURANCE-if-carrier {
		@include text-element(15px, 15px);
		width: 419px;
		height: 42px;
	}

	// Group element with positioned-element mixin
	.group-69 {
		@include positioned-element(79px, 72px, 715px, 401px);
		background-color: transparent;
		border: none;
	}

	// Text wrapper with transparent color
	.text-wrapper-43 {
		@include text-element(46px, 15px, 14px);
		color: transparent;
		line-height: 14px;
		white-space: nowrap;
	}
}

.element-proview-AWB {
	// Group element with positioned-element mixin
	.group-70 {
		@include positioned-element(64px, 72px, 715px, 661px);
		background-color: transparent;
		border: none;
	}

	// Overlap element with relative-positioned-box mixin
	.overlap-25 {
		@include relative-positioned-box(62px, 72px);
	}
}

.element-proview-AWB {
	// Text wrapper with standard styling
	.text-wrapper-44 {
		@include text-element(15px, 5px);
	}

	// Group element with positioning
	.group-71 {
		@include positioned-element(335px, 72px, 715px, 71px);
	}

	// Overlap group with background sizing
	.overlap-group-11 {
		@include positioned-element(166px, 27px, 0, 165px);
		background-size: 100% 100%;
	}

	// Text wrapper with centered alignment
	.text-wrapper-45 {
		@include text-element(7px, 17px);
		text-align: center;
	}
}

.element-proview-AWB {
	// Group element with positioned-element mixin
	.group-72 {
		@include positioned-element(241px, 72px, 644px, 661px);
		background-color: transparent;
		border: none;
	}

	// Overlap element with relative-positioned-box mixin
	.overlap-26 {
		@include relative-positioned-box(239px, 72px);
	}
}

.element-proview-AWB {
	// Group elements with positioned-element mixin
	.group-73 {
		@include positioned-element(122px, 72px, 644px, 1201px);
		background-color: transparent;
		border: none;
	}

	.group-74 {
		@include positioned-element(305px, 72px, 644px, 899px);
		background-color: transparent;
		border: none;
	}

	// Overlap element with relative-positioned-box mixin
	.overlap-27 {
		@include relative-positioned-box(303px, 72px);
	}

	// Rectangle element with positioned-element mixin
	.rectangle-29 {
		@include positioned-element(303px, 27px, -1px, -1px);
		background-color: $light-gray;
	}

	// Text wrapper with text-element mixin
	.text-wrapper-46 {
		@include text-element(6px, 69px);
	}
}

.element-proview-AWB {
	// Group element with positioned-element mixin
	.group-75 {
		@include positioned-element(333px, 72px, 573px, 331px);
		background-color: transparent;
		border: none;
	}
}

// PDF Export Styles
.export-button {
	&:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	mat-icon {
		margin-right: 8px;
	}
}

// Print-specific styles for better PDF output
@media print {
	.button-footer {
		display: none !important;
	}

	.overlap-wrapper {
		box-shadow: none !important;
		border: none !important;
	}
}

/*
 * SCSS Optimization Summary:
 *
 * 1. Variable Consolidation:
 *    - Defined common color variables ($white, $border-color, $text-color, $light-gray)
 *    - Created standard styling variables ($border-style, $box-shadow)
 *
 * 2. Mixin Architecture:
 *    - Created base mixins for common styling patterns:
 *      - base-styles: Common positioning and border styles
 *      - text-element: Typography and text styling with parameters
 *      - positioned-element: Absolute positioning with dimensions
 *      - relative-positioned-box: Relative positioning with dimensions
 *      - vertical-divider: Specialized divider styling
 *      - centered-text-wrapper: Centered text alignment
 *
 * 3. Selector Optimization:
 *    - Nested selectors under parent .element-proview-AWB
 *    - Grouped similar elements with consistent patterns
 *    - Consolidated duplicate styling across multiple elements
 *
 * 4. Code Structure Improvements:
 *    - Organized related elements together with descriptive comments
 *    - Parameterized mixins for flexible component styling
 *    - Maintained original layout specifications while reducing code size
 *
 * These optimizations significantly reduced file size while improving maintainability
 * and making future styling changes more efficient.
 */

// Optimization notes:
// This file has been optimized by:
// 1. Adding common variables for colors and repeated values
// 2. Creating reusable mixins for common style patterns
// 3. Applying mixins to reduce code duplication
// 4. Standardizing positioning and styling across similar elements
