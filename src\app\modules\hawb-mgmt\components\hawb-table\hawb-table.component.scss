.orll-hawb-table {
	&__container {
		min-height: 400px;
	}

	&__create {
		display: flex;
		justify-content: flex-end;
	}

	&__fhl-header {
		display: flex;
		margin-bottom: 10px;
		.bold {
			font-size: 18px;
			font-weight: 600;
		}
	}

	&__mat {
		.hawb-select-width {
			width: 50px;
		}

		.hawb-number-width {
			width: 180px;
		}

		.hawb-shipper-width {
			width: 200px;
		}

		.hawb-consignee-width {
			width: 200px;
		}

		.hawb-description-width {
			width: 250px;
		}

		.hawb-airport-width {
			width: 50px;
		}

		.hawb-date-width {
			width: 200px;
		}

		.event-date-width {
			width: 200px;
		}

		.latest-status-width {
			width: 180px;
		}

		.mawb-number-width {
			width: 200px;
		}

		.hawb-date-share {
			width: 50px;
		}

		.weight-width {
			width: 50px;
		}

		.quantity-width {
			width: 50px;
		}

		.slac-width {
			width: 50px;
		}

		.content-code-width {
			width: 200px;
		}

		.other-info-width {
			width: 200px;
		}

		.hawb-number__link {
			color: var(--iata-blue-primary);
			text-decoration: none;
			cursor: pointer;

			&:hover {
				text-decoration: underline;
			}
		}
	}
}
